<?php
#region region DOCS
/** @var ConfigWeb $configuraciones_web */

use App\classes\ConfigWeb;
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<!-- ========== Meta Tags ========== -->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<!-- ======== Page title ============ -->
	<title><?php echo APP_NAME; ?> | Contáctanos</title>
	
	<?php require_once __ROOT__ . '/views/web/head_section.view.php'; ?>
</head>

<body class="body-wrapper">
<!-- welcome content start from here -->
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
	<form id="formulario" action="contactanos" method="POST">
		<input type="hidden" name="sub_enviado">
		
		<div id="contactanos" class="contact-us-wrapper section-padding pt-5">
			<div class="container">
				<div class="row eq-height">
					<div class="col-lg-5 col-12">
						<div class="contact-us-sidebar mt-5 mt-lg-0">
							<div class="live-chat">
								<h5>Impulsa tus proyectos con <b><?php echo NOMBRE_EMPRESA_ABREV; ?></b></h5>
								<p>
									En un entorno donde la infraestructura energética y la ejecución
									técnica marcan la diferencia, contar con un aliado confiable es clave.
									En <b><?php echo NOMBRE_EMPRESA_ABREV; ?></b>, convertimos la experiencia
									en soluciones integrales para tus proyectos de ingeniería, montajes
									eléctricos y servicios de mantenimiento.
								</p>
								<p>
									🚧 No dejes que los retos operativos detengan tu crecimiento.
									Nosotros te ayudamos a avanzar con seguridad, eficiencia y cumplimiento.
								</p>
								<p class="mt-3">
									¿Listo para construir el futuro de tu organización?
								</p>
								<p class="mt-3">
									📩 Contáctanos hoy y descubre cómo nuestras soluciones a la medida
									pueden potenciar tus operaciones.
								</p>
							</div>
							<div class="contact-info">
								<div class="single-info">
									<div class="icon">
										<i class="flaticon-email"></i>
									</div>
									<div class="text">
										<span>Correo</span>
										<h5><a href="mailto:<?php echo $configuraciones_web->getCorreo(); ?>"><?php echo $configuraciones_web->getCorreo(); ?></a></h5>
									</div>
								</div>
								<div class="single-info">
									<div class="icon">
										<i class="flaticon-phone-call-1"></i>
									</div>
									<div class="text">
										<span>Teléfono</span>
										<h5>
											<a href="https://wa.me/<?php echo sanitize_phone_number($configuraciones_web->getTelefono()); ?>?text=<?php echo WHATSAPP_SALUDO; ?>" target="_blank">
												<?php echo $configuraciones_web->getTelefono(); ?>
											</a>
										</h5>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-7 col-12">
						<div class="contact-form">
							<h2>Contáctanos</h2>
							<!-- BEGIN row -->
							<div class="row">
								<div class="col-md-6 col-xs-12">
									<div class="single-personal-info">
										<input type="text" name="nombre" placeholder="Nombre">
									</div>
								</div>
								<div class="col-md-6 col-xs-12">
									<div class="single-personal-info">
										<input type="email" name="correo" placeholder="Correo">
									</div>
								</div>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row">
								<div class="col-md-6 col-xs-12">
									<div class="single-personal-info">
										<input type="text" name="telefono" placeholder="Teléfono">
									</div>
								</div>
								<div class="col-md-6 col-xs-12">
									<div class="single-personal-info">
										<input type="text" name="asunto" placeholder="Asunto">
									</div>
								</div>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row">
								<div class="col-md-12 col-xs-12">
									<div class="single-personal-info">
										<textarea name="mensaje" placeholder="Mensaje"></textarea>
									</div>
								</div>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row">
								<div class="col-md-12 col-xs-12">
									<input type="submit" id="sub_enviar" name="sub_enviar" class="submit-btn submit-btn-blue w-100" value="Enviar">
								</div>
							</div>
							<!-- END row -->
							<span class="form-message"></span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php'; ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php'; ?>

<?php #region region JS evento submit enviar ?>
<script>
    document.getElementById('sub_enviar').addEventListener('click', function (event) {
        event.preventDefault(); // Prevent default form submission behavior
        
        // Change the button text to "Enviando..."
        const button    = document.getElementById('sub_enviar');
        button.value    = "Enviando...";
        button.disabled = true;
        
        // Explicitly submit the form
        document.getElementById('formulario').submit();
    });
</script>
<?php #endregion JS evento submit enviar ?>
<?php #endregion JS ?>

</body>

</html>