<?php
#region region DOCS
/** @var ConfigWeb $configuraciones_web */

use App\classes\ConfigWeb;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<title><?php echo APP_NAME; ?> | Políticas</title>
	
	<?php // Asegúrate de que __ROOT__ y RUTA_RESOURCES estén definidos correctamente ?>
	<?php require_once __ROOT__ . '/views/web/head_section.view.php'; // Modificado ?>
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES; ?>css/fab_politicas_web.css">

</head>

<body class="body-wrapper">
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php';  ?>

<main>
	<?php #region region form ?>
	<form action="#" method="POST" style="padding: 40px">
		<div class="policy-container">
			<div class="container">
				<h2>POLÍTICA DE RESPONSABILIDAD Y CUMPLIMIENTO DE TAREAS</h2>
				<h3>Servicios de Ingeniería Montajes y Proyectos <?php echo NOMBRE_EMPRESA_ABREV; ?></h3>
				
				<h3>1. Objetivo</h3>
				<p>Establecer los lineamientos de responsabilidad, cumplimiento y calidad en la ejecución de tareas asignadas al personal operativo, técnico, administrativo y contratistas vinculados a proyectos desarrollados por <?php echo NOMBRE_EMPRESA_ABREV; ?>, con el fin de garantizar la eficiencia, cumplimiento de plazos, calidad técnica y respeto a los compromisos adquiridos con clientes, aliados y autoridades.</p>
				
				<h3>2. Alcance</h3>
				<p>Esta política aplica a todos los colaboradores de planta, contratistas, subcontratistas, proveedores de servicios técnicos, administrativos y aliados comerciales que intervienen en los proyectos o actividades operativas de <?php echo NOMBRE_EMPRESA_ABREV; ?>, tanto en campo como en oficina.</p>
				
				<h3>3. Principios Generales</h3>
				<ul>
					<li><strong>Diligencia y compromiso:</strong> Cada persona debe ejecutar sus funciones con profesionalismo, atención al detalle y actitud proactiva.</li>
					<li><strong>Cumplimiento de plazos:</strong> Las actividades deben entregarse dentro de los tiempos establecidos en los cronogramas, salvo justificación válida.</li>
					<li><strong>Calidad del trabajo:</strong> Todo entregable debe cumplir con los estándares técnicos, normativos y de seguridad definidos por la empresa o el cliente.</li>
					<li><strong>Ética profesional:</strong> Se espera un comportamiento transparente, respetuoso, honesto y leal hacia la empresa y sus objetivos.</li>
					<li><strong>Uso responsable de recursos:</strong> Las herramientas, materiales, documentación y medios provistos por la empresa deben ser utilizados adecuadamente y para fines laborales.</li>
				</ul>
				
				<h3>4. Responsabilidades de los Colaboradores y Contratistas</h3>
				<ul>
					<li>Cumplir con las tareas asignadas en los términos y condiciones definidos en los proyectos.</li>
					<li>Mantener comunicación clara y oportuna sobre avances, riesgos o requerimientos adicionales.</li>
					<li>Notificar con antelación cualquier retraso o situación que afecte la entrega.</li>
					<li>Respetar la confidencialidad de la información a la que tenga acceso.</li>
					<li>No delegar funciones ni subcontratar tareas sin autorización expresa de la empresa.</li>
					<li>Actuar conforme a la legislación laboral, ambiental, de salud y seguridad vigente.</li>
				</ul>
				
				<h3>5. Plazos y Entregas de Proyectos</h3>
				<ul>
					<li>Todo proyecto, tarea o actividad tendrá un cronograma de ejecución, el cual deberá ser cumplido rigurosamente.</li>
					<li>En caso de retrasos por fuerza mayor o situaciones justificadas, el colaborador deberá informar con al menos 48 horas de anticipación, proponiendo un plan de contingencia.</li>
					<li>Cualquier modificación en los plazos deberá ser aprobada por el responsable directo del proyecto.</li>
				</ul>
				
				<h3>6. Control de Calidad y Revisión de Entregables</h3>
				<ul>
					<li><?php echo NOMBRE_EMPRESA_ABREV; ?> se reserva el derecho de revisar periódicamente los trabajos entregados para verificar su conformidad con los estándares de calidad.</li>
					<li>En caso de no conformidad, el responsable deberá realizar los ajustes necesarios en un plazo no mayor a tres (3) días hábiles.</li>
					<li>El incumplimiento reiterado de los estándares dará lugar a medidas disciplinarias o contractuales.</li>
				</ul>
				
				<h3>7. Sanciones por Incumplimiento</h3>
				<p>Dependiendo de la gravedad del caso, el incumplimiento de esta política puede conllevar:</p>
				<ul>
					<li>Llamado de atención escrito.</li>
					<li>Suspensión de actividades o tareas.</li>
					<li>Retención parcial o total del pago, si el servicio no cumple con lo pactado.</li>
					<li>Terminación del contrato o vínculo comercial.</li>
					<li>Acciones legales, si el incumplimiento genera perjuicios económicos, contractuales o reputacionales.</li>
				</ul>
				
				<h3>8. Confidencialidad y Responsabilidad Legal</h3>
				<p>Toda la información técnica, financiera, contractual o estratégica a la que acceda el colaborador o contratista se considera confidencial. Su uso indebido o divulgación no autorizada será sancionado conforme a lo dispuesto en la Ley 1581 de 2012 (protección de datos personales), la Ley 1273 de 2009 (delitos informáticos), y demás normas aplicables.</p>
				
				<h3>9. Vigencia y Actualizaciones</h3>
				<p>Esta política entra en vigencia desde la fecha de su publicación y podrá ser modificada en cualquier momento por <?php echo NOMBRE_EMPRESA_ABREV; ?>, conforme a la evolución de sus procesos, requerimientos legales o necesidades operativas.</p>
				
				<div class="fechas">
					<p>
						<strong>Fecha de entrada en vigencia:</strong> Julio 2025<br>
						<strong>Última actualización:</strong> Julio 2025
					</p>
				</div>
				
				<hr>
				<p>📩 Para más información, contáctanos en: <a href="mailto:<?php echo $configuraciones_web->getCorreo(); ?>"><?php echo $configuraciones_web->getCorreo(); ?></a></p>
			
			</div>
		</div>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php';  ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php';  ?>
<?php #endregion JS ?>

</body>
</html>
