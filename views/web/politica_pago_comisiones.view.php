<?php
#region region DOCS
/** @var ConfigWeb $configuraciones_web */

use App\classes\ConfigWeb;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<title><?php echo APP_NAME;  ?> | Políticas</title>
	
	<?php require_once __ROOT__ . '/views/web/head_section.view.php';  ?>
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES; ?>css/fab_politicas_web.css">
</head>

<body class="body-wrapper">
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php';  ?>

<main>
	<?php #region region form ?>
	<form action="#" method="POST" style="padding: 40px">
		<div class="policy-container">
			<div class="container">
				<h2>POLÍTICA DE PAGO Y COMISIONES</h2>
				<h3>Servicios de Ingeniería Montajes y Proyectos <?php echo NOMBRE_EMPRESA_ABREV; ?></h3>
				
				<h3>1. Objetivo</h3>
				<p>Establecer los lineamientos, condiciones y procedimientos aplicables para la liquidación, validación, pago y gestión de comisiones, honorarios o contraprestaciones económicas a favor de contratistas, subcontratistas, proveedores, aliados comerciales y demás terceros que presten servicios a <?php echo NOMBRE_EMPRESA_ABREV; ?>, garantizando legalidad, equidad y transparencia.</p>
				
				<h3>2. Alcance</h3>
				<p>Esta política aplica a todas las personas naturales o jurídicas que tengan una relación comercial, contractual o de prestación de servicios con <?php echo NOMBRE_EMPRESA_ABREV; ?>, ya sea a nivel nacional o internacional, y que generen derecho a pago por conceptos previamente pactados.</p>
				
				<h3>3. Principios Rectores</h3>
				<ul>
					<li><strong>Legalidad:</strong> Todos los pagos se realizarán en cumplimiento de la normativa laboral, fiscal y comercial vigente en Colombia.</li>
					<li><strong>Transparencia:</strong> Las condiciones de pago, valores y plazos serán previamente definidos en contrato o acuerdo escrito.</li>
					<li><strong>Equidad:</strong> Las comisiones y contraprestaciones se reconocerán con base en los resultados, alcance de servicios y entregables establecidos.</li>
					<li><strong>Oportunidad:</strong> Los pagos se ejecutarán dentro de los plazos pactados, siempre y cuando se cumplan los requisitos administrativos y técnicos.</li>
				</ul>
				
				<h3>4. Modalidades de Pago</h3>
				<p>Los pagos se podrán realizar bajo las siguientes modalidades, según el tipo de contrato:</p>
				<ul>
					<li><strong>Por avance de obra:</strong> Se liquida contra certificaciones técnicas de avance, verificadas y aprobadas por el supervisor del proyecto.</li>
					<li><strong>Por entrega de hitos:</strong> Se realiza al cumplimiento de fases específicas del proyecto.</li>
					<li><strong>Por proyecto terminado:</strong> Contra acta de entrega final y aceptación del cliente.</li>
					<li><strong>Por comisión comercial:</strong> Aplicable a aliados o gestores que generen negocios; el porcentaje será definido en acuerdo previo.</li>
				</ul>
				
				<h3>5. Procedimiento para la Ejecución de Pagos</h3>
				<h4>5.1 Registro y documentación</h4>
				<p>El beneficiario deberá registrar su información bancaria y tributaria actualizada ante el área contable de <?php echo NOMBRE_EMPRESA_ABREV; ?></p>
				<h4>5.2 Reporte y validación</h4>
				<p>El contratista o aliado deberá presentar el reporte de horas, entregables o evidencia del cumplimiento contractual, conforme a los formatos establecidos.</p>
				<h4>5.3 Facturación o cuenta de cobro</h4>
				<ul>
					<li>Si es responsable de IVA, deberá emitir factura electrónica con los requisitos legales.</li>
					<li>En caso contrario, podrá presentar cuenta de cobro con los respectivos soportes.</li>
				</ul>
				<h4>5.4 Aprobación y programación del pago</h4>
				<p>Una vez validada la documentación por las áreas técnica y financiera, se programará el pago dentro del plazo correspondiente.</p>
				
				<h3>6. Plazos de Pago Estándar</h3>
				<ul>
					<li><strong>Pagos por horas trabajadas:</strong> Hasta 15 días hábiles después de la validación.</li>
					<li><strong>Pagos por hitos o entregables:</strong> Hasta 30 días hábiles después de su aceptación.</li>
					<li><strong>Pagos por comisiones:</strong> Hasta 30 días hábiles contados desde el recaudo efectivo del cliente.</li>
				</ul>
				
				<h3>7. Deducciones y Retenciones</h3>
				<p>Se aplicarán las siguientes deducciones conforme a la legislación colombiana:</p>
				<ul>
					<li>Retención en la fuente, según régimen del beneficiario.</li>
					<li>Aportes a seguridad social, cuando aplique.</li>
					<li>Descuentos por incumplimiento de entregables, retrasos o penalidades contractuales.</li>
				</ul>
				
				<h3>8. Reclamos y Ajustes</h3>
				<p>Cualquier diferencia, error o inconformidad en el valor liquidado deberá ser reportada por el beneficiario dentro de los 5 días hábiles posteriores al pago. Una vez validado el caso, <?php echo NOMBRE_EMPRESA_ABREV; ?> podrá realizar ajustes en la siguiente liquidación.</p>
				
				<h3>9. Excepciones y Casos Especiales</h3>
				<ul>
					<li>Si un proyecto es cancelado de forma anticipada, se evaluará el porcentaje de avance para definir el pago proporcional.</li>
					<li>Si el cliente final incumple el pago, <?php echo NOMBRE_EMPRESA_ABREV; ?> podrá aplazar la comisión correspondiente previa notificación al tercero implicado.</li>
				</ul>
				
				<h3>10. Vigencia y Actualización</h3>
				<p>Esta política entra en vigencia a partir de su publicación y podrá ser modificada por la empresa en cualquier momento, con base en actualizaciones legales o cambios en los procesos internos. Las nuevas versiones serán debidamente comunicadas a los interesados.</p>
				
				<div class="fechas">
					<p>
						<strong>Fecha de entrada en vigencia:</strong> Julio 2025<br>
						<strong>Última actualización:</strong> Julio 2025
					</p>
				</div>
				
				<hr>
				<p>📩 Para más información, contáctanos en: <a href="mailto:<?php echo $configuraciones_web->getCorreo(); ?>"><?php echo $configuraciones_web->getCorreo(); ?></a></p>
			
			</div>
		</div>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php';  ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php';  ?>
<?php #endregion JS ?>

</body>
</html>
