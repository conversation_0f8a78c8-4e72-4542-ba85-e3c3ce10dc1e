<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es">

<head>
	<!-- ========== Meta Tags ========== -->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="SERIMPRO">
	<meta name="description" content="Proyectos realizados por SERIMPRO - Diseño, construcción y mantenimiento de obras civiles y sistemas eléctricos">
	<meta name="keywords" content="proyectos, obras civiles, sistemas eléctricos, construcción, SERIMPRO">
	<!-- ======== Page title ============ -->
	<title><?php echo APP_NAME; ?> | Proyectos</title>
	
	<?php require_once __ROOT__ . '/views/web/head_section.view.php'; ?>
	
	<!-- Fancybox CSS for mixed media gallery -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css">
	
	<!-- Custom CSS for Proyectos -->
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>css/proyectos_2.css">
</head>

<body class="body-wrapper">
<!-- welcome content start from here -->
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
	<form action="proyectos" method="POST">
		<!-- Hero Section -->
		<section class="about-section section-padding pt-5">
			<div class="container">
				<div class="row align-items-center">
					<div class="col-xl-12 col-12">
						<div class="block-contents text-center">
							<div class="section-title">
								<h2 class="wow fadeInUp2" data-wow-delay="0.4s">
									PROYECTOS REALIZADOS
								</h2>
							</div>
							<p class="wow fadeInUp2 mt-3" data-wow-delay="0.6s">
								Conoce algunos de los proyectos más destacados que hemos desarrollado para nuestros clientes.
								Cada proyecto refleja nuestro compromiso con la calidad, la innovación y la satisfacción del cliente.
							</p>
						</div>
					</div>
				</div>
			</div>
		</section>
		
		<!-- Projects Grid Section -->
		<section class="projects-section section-padding">
			<div class="container">
				<div class="row">
					<?php if (!empty($proyectos)): ?>
						<?php foreach ($proyectos as $index => $proyecto): ?>
							<!-- DEBUG: Let's see what data we're rendering -->
							<!-- Project <?php echo $index; ?>: <?php echo $proyecto['project_name']; ?> | Client: <?php echo $proyecto['client_name']; ?> | Location: <?php echo $proyecto['location']; ?> -->
							<div class="col-lg-4 col-md-6 col-12 mb-4">
								<div class="project-card wow fadeInUp2" data-wow-delay="<?php echo 0.2 + ($index * 0.1); ?>s">
									<!-- Project Featured Image -->
									<div class="project-image">
										<img src="<?php echo RUTA_RESOURCES ?>images/proyectos/<?php echo $proyecto['featured_image']; ?>"
										     alt="<?php echo htmlspecialchars($proyecto['project_name']); ?>"
										     class="img-fluid">
										<div class="project-overlay">
											<div class="project-overlay-content">
												<!--<h5><?php /*echo htmlspecialchars($proyecto['project_name']); */?></h5>
												<p><i class="fas fa-map-marker-alt"></i> <?php /*echo htmlspecialchars($proyecto['location']); */?></p>-->
												<button type="button" class="btn btn-primary btn-sm view-gallery"
												        data-project-id="<?php echo $proyecto['id']; ?>">
													<i class="fas fa-images"></i> Ver Galería
												</button>
											</div>
										</div>
									</div>
									
									<!-- Project Info -->
									<div class="project-info">
										<!-- Project Details -->
										<div class="project-details">
											<h4 class="project-title"><?php echo htmlspecialchars($proyecto['project_name']); ?></h4>
											<h6 class="client-name">
												<?php if (!empty($proyecto['client_name'])): ?>
													<!-- Client Logo -->
													<img src="<?php echo RUTA_RESOURCES ?>images/clientes/<?php echo $proyecto['client_logo']; ?>"
													     alt="<?php echo htmlspecialchars($proyecto['client_name']); ?>"
													     class="img-fluid" style="width: 40px">
													<!-- Client Name -->
													<?php echo htmlspecialchars($proyecto['client_name']); ?>
												<?php endif; ?>
											</h6>
											<p class="project-location">
												<i class="fas fa-map-marker-alt"></i>
												<?php echo htmlspecialchars($proyecto['location']); ?>
											</p>
											<p class="project-description">
												<?php echo htmlspecialchars($proyecto['description']); ?>
											</p>
										</div>
									</div>
									
									<!-- Hidden gallery for Fancybox -->
									<div class="hidden-gallery" style="display: none;">
										<?php foreach ($proyecto['media'] as $mediaIndex => $media): ?>
											<?php if ($media['type'] === 'image'): ?>
												<a href="<?php echo RUTA_RESOURCES ?>images/proyectos/<?php echo $media['file']; ?>"
												   data-fancybox="gallery-<?php echo $proyecto['id']; ?>"
												   data-thumb="<?php echo RUTA_RESOURCES ?>images/proyectos/<?php echo $media['file']; ?>"
												   data-caption="<?php echo htmlspecialchars($proyecto['project_name']); ?> - Imagen <?php echo $mediaIndex + 1; ?>"
												   class="gallery-item">
												</a>
											<?php else: ?>
												<a href="<?php echo RUTA_RESOURCES ?>images/proyectos/<?php echo $media['file']; ?>"
												   data-fancybox="gallery-<?php echo $proyecto['id']; ?>"
												   data-thumb="<?php echo RUTA_RESOURCES ?>images/video-placeholder.svg"
												   data-caption="<?php echo htmlspecialchars($proyecto['project_name']); ?> - Video"
												   class="gallery-item">
												</a>
											<?php endif; ?>
										<?php endforeach; ?>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					<?php else: ?>
						<div class="col-12 text-center">
							<div class="no-projects">
								<i class="fas fa-folder-open fa-3x mb-3 text-muted"></i>
								<h4>Próximamente</h4>
								<p>Estamos preparando la galería de nuestros proyectos más destacados.</p>
							</div>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</section>
		
		<!-- Call to Action Section -->
		<section class="cta-section section-padding bg-light">
			<div class="container">
				<div class="row align-items-center">
					<div class="col-lg-8 col-12">
						<div class="cta-content">
							<h3 class="wow fadeInUp2" data-wow-delay="0.3s">
								¿Tienes un proyecto en mente?
							</h3>
							<p class="wow fadeInUp2" data-wow-delay="0.4s">
								Contáctanos y permítenos ser parte de tu próximo proyecto exitoso.
								Nuestro equipo de expertos está listo para asesorarte.
							</p>
						</div>
					</div>
					<div class="col-lg-4 col-12 text-lg-end text-center">
						<a href="contactanos" class="btn btn-primary btn-lg wow fadeInUp2" data-wow-delay="0.5s">
							<i class="fas fa-phone"></i> Contáctanos
						</a>
					</div>
				</div>
			</div>
		</section>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php'; ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php'; ?>

<!-- Fancybox JS for mixed media gallery -->
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>

<!-- Custom JS for Proyectos -->
<script src="<?php echo RUTA_RESOURCES ?>js/proyectos_1.js"></script>

<?php #endregion JS ?>

</body>

</html>
