<?php #region docs
/** @var ServicioCategoria[] $categorias */
/** @var Usuario $logged_usuario_info */

use App\classes\ServicioCategoria;
use App\classes\Usuario;

#endregion docs
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8" />
    <title>Categorías de Servicios | <?php echo APP_NAME; ?></title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>

    <!-- DataTables CSS -->
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet" />
</head>

<body>
    <!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed">
        <!-- #header -->
        <?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>

        <!-- #sidebar -->
        <?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>

        <!-- BEGIN #content -->
        <div id="content" class="app-content">
            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center justify-content-between mb-3">
                <div>
                    <h4 class="mb-1">Categorías de Servicios</h4>
                    <p class="text-muted mb-0">Administrar categorías de servicios del sistema</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalCategoria" onclick="abrirModalCrear()">
                    <i class="fa fa-plus me-1"></i> Crear Nueva
                </button>
            </div>
            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region CATEGORIES TABLE ?>
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Lista de Categorías</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table id="data-table-categorias" class="table table-bordered align-middle">
                            <thead>
                                <tr>
                                    <th width="120" class="text-center">Acciones</th>
                                    <th>Descripción</th>
                                    <th width="150">Icono</th>
                                    <th width="100" class="text-center">Prioridad</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categorias as $categoria): ?>
                                <tr>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-xs btn-warning me-1"
                                                onclick="abrirModalEditar(<?php echo $categoria->getId(); ?>)"
                                                title="Editar categoría">
                                            <i class="fa fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger"
                                                onclick="eliminarCategoria(<?php echo $categoria->getId(); ?>)"
                                                title="Eliminar categoría">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                    <td><?php echo htmlspecialchars($categoria->getDescripcion() ?? ''); ?></td>
                                    <td>
                                        <i class="<?php echo htmlspecialchars($categoria->getIcono() ?? ''); ?> me-2"></i>
                                        <small class="text-muted"><?php echo htmlspecialchars($categoria->getIcono() ?? ''); ?></small>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary"><?php echo $categoria->getPrioridad(); ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php #endregion CATEGORIES TABLE ?>
        </div>
        <!-- END #content -->

        <!-- BEGIN scroll-top-btn -->
        <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region MODAL CATEGORIA ?>
    <!-- Modal for Create/Edit Category -->
    <div class="modal fade" id="modalCategoria" tabindex="-1" aria-labelledby="modalCategoriaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCategoriaLabel">Crear Nueva Categoría</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formCategoria" novalidate>
                    <div class="modal-body">
                        <input type="hidden" id="categoriaId" name="id" value="">
                        <input type="hidden" id="action" name="action" value="crear">

                        <div class="mb-3">
                            <label for="descripcion" class="form-label">
                                Descripción <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="descripcion" name="descripcion" 
                                   required maxlength="100" placeholder="Ingrese la descripción de la categoría">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Mínimo 3 caracteres, máximo 100 caracteres</div>
                        </div>

                        <div class="mb-3">
                            <label for="icono" class="form-label">
                                Icono <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="icono" name="icono" 
                                   required placeholder="Ej: fa fa-home, bi bi-house">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Clase CSS del icono (FontAwesome, Bootstrap Icons, etc.)</div>
                        </div>

                        <div class="mb-3">
                            <label for="prioridad" class="form-label">
                                Prioridad <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="prioridad" name="prioridad" 
                                   required min="1" max="999" placeholder="Ingrese la prioridad">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Número entero positivo para ordenar las categorías</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnSubmit">
                            <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
                            Crear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion MODAL CATEGORIA ?>

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

    <!-- DataTables JS -->
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>

    <script src="<?php echo RUTA_RESOURCES ?>js/lservicios_categorias.js"></script>
    <?php #endregion JS ?>
</body>
</html>
