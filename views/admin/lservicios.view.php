<?php #region docs
/** @var Servicio[] $servicios */
/** @var ServicioCategoria[] $categorias */
/** @var Usuario $logged_usuario_info */

use App\classes\Servicio;
use App\classes\ServicioCategoria;
use App\classes\Usuario;

#endregion docs
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8" />
    <title>Servicios | <?php echo APP_NAME; ?></title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>

    <!-- DataTables CSS -->
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <link href="<?php echo RUTA_RESOURCES ?>css/lservicios.css" rel="stylesheet" />
</head>
<body>
    <!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed">
        <!-- BEGIN #header -->
        <?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
        <!-- END #header -->

        <!-- BEGIN #sidebar -->
        <?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
        <!-- END #sidebar -->

        <!-- BEGIN #content -->
        <div id="content" class="app-content">
            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center justify-content-between mb-3">
                <div>
                    <h4 class="mb-1">Servicios</h4>
                    <p class="text-muted mb-0">Administrar servicios del sistema</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalServicio" onclick="abrirModalCrear()">
                    <i class="fa fa-plus me-1"></i> Crear Nuevo
                </button>
            </div>
            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region SERVICES TABLE ?>
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Lista de Servicios</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table id="data-table-servicios" class="table table-bordered align-middle">
                            <thead>
                                <tr>
                                    <th width="120" class="text-center">Acciones</th>
                                    <th>Categoría</th>
                                    <th>Descripción</th>
                                    <th width="100" class="text-center">Prioridad</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($servicios as $servicio): ?>
                                <tr>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-xs btn-warning me-1"
                                                onclick="abrirModalEditar(<?php echo $servicio->getId(); ?>)"
                                                title="Editar servicio">
                                            <i class="fa fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger"
                                                onclick="eliminarServicio(<?php echo $servicio->getId(); ?>)"
                                                title="Eliminar servicio">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                    <td>
                                        <i class="<?php echo htmlspecialchars($servicio->getJoinedCategoriaIcono() ?? 'fa fa-tag'); ?> me-2"></i>
                                        <?php echo htmlspecialchars($servicio->getJoinedCategoriaDescripcion() ?? 'Sin categoría'); ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($servicio->getDescripcion() ?? ''); ?></td>
                                    <td class="text-center">
                                        <span class="badge bg-primary"><?php echo $servicio->getPrioridad(); ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php #endregion SERVICES TABLE ?>
        </div>
        <!-- END #content -->

        <!-- BEGIN scroll-top-btn -->
        <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region MODAL SERVICIO ?>
    <!-- Modal for Create/Edit Service -->
    <div class="modal fade" id="modalServicio" tabindex="-1" aria-labelledby="modalServicioLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalServicioLabel">Crear Nuevo Servicio</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formServicio" novalidate>
                    <div class="modal-body">
                        <input type="hidden" id="servicioId" name="id" value="">
                        <input type="hidden" id="action" name="action" value="crear">

                        <div class="mb-3">
                            <label for="id_servicio_categoria" class="form-label">
                                Categoría de servicio <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="id_servicio_categoria" name="id_servicio_categoria" required>
                                <option value="">Seleccione una categoría</option>
                                <?php foreach ($categorias as $categoria): ?>
                                <option value="<?php echo $categoria->getId(); ?>">
                                    <?php echo htmlspecialchars($categoria->getDescripcion()); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Seleccione la categoría a la que pertenece el servicio</div>
                        </div>

                        <div class="mb-3">
                            <label for="descripcion" class="form-label">
                                Descripción <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="descripcion" name="descripcion" 
                                   required maxlength="255" placeholder="Ingrese la descripción del servicio">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Mínimo 3 caracteres, máximo 255 caracteres</div>
                        </div>

                        <div class="mb-3">
                            <label for="prioridad" class="form-label">
                                Prioridad <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="prioridad" name="prioridad" 
                                   required min="1" max="999" placeholder="Ingrese la prioridad">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Número entero positivo para ordenar los servicios</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnSubmit">
                            <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
                            Crear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion MODAL SERVICIO ?>

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

    <!-- DataTables JS -->
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>

    <script src="<?php echo RUTA_RESOURCES ?>js/lservicios.js"></script>
    <?php #endregion JS ?>
</body>
</html>
