<?php
#region docs
/** @var string $username_input */ // Variable para repoblar el campo usuario
/** @var string|null $error_display */ // Para controlar la visibilidad del error
/** @var string $error_text */ // Mensaje de error a mostrar
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Login</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="-i-ndex"/>
	<meta content="" name="author"/>
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
</head>
<body class='pace-top'>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->


<!-- BEGIN #app -->
<div id="app" class="app">
	<!-- BEGIN login -->
	<div class="login login-v1">
		<!-- BEGIN login-container -->
		<div class="login-container">
			<!-- BEGIN login-header -->
			<div class="login-header">
				<div class="brand">
					<div class="d-flex align-items-center">
						<span class="logo"></span> <?php echo APP_NAME; ?>
					</div>
				</div>
				<div class="icon">
					<i class="fa fa-lock"></i>
				</div>
			</div>
			<!-- END login-header -->
			
			<!-- BEGIN login-body -->
			<div class="login-body">
				<!-- BEGIN login-content -->
				<div class="login-content fs-13px">
					<?php #region region FORM ?>
					<form action="login" method="POST" class="needs-validation" novalidate>
						<h3 class="mb-2">Ingrese sus credenciales</h3>
						<?php // --- Campo Usuario --- ?>
						<div class="mb-3">
							<label for="usuario" class="form-label">Usuario:</label>
							<input type="text" id="usuario" name="usuario" value="<?php echo htmlspecialchars($username_input, ENT_QUOTES, 'UTF-8'); ?>" class="form-control fs-13px h-45px" required autofocus/>
							<div class="invalid-feedback">
								Por favor, ingresa tu nombre de usuario.
							</div>
						</div>
						
						<?php // --- Campo Clave --- ?>
						<div class="mb-3">
							<label for="clave" class="form-label">Contraseña:</label>
							<input type="password" id="clave" name="clave" class="form-control fs-13px h-45px" required/>
							<div class="invalid-feedback">
								Por favor, ingresa tu contraseña.
							</div>
						</div>
						
						<div class="login-buttons mt-3">
							<button type="submit" id="logearse" class="btn h-45px btn-success d-block w-100 btn-lg">
								Ingresar
							</button>
						</div>
					</form>
					<?php #endregion FORM ?>
				</div>
				<!-- END login-content -->
			</div>
			<!-- END login-body -->
		</div>
		<!-- END login-container -->
	</div>
	<!-- END login -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script>
    // Script para activar la validación de formularios de Bootstrap
    (() => {
        'use strict';
        
        // Obtener todos los formularios a los que queremos aplicar estilos de validación personalizados de Bootstrap
        const forms = document.querySelectorAll('.needs-validation');
        
        // Bucle sobre ellos y evitar el envío si no son válidos
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault(); // Evita el envío del formulario
                    event.stopPropagation(); // Detiene la propagación del evento
                }
                
                form.classList.add('was-validated'); // Aplica las clases de Bootstrap para mostrar feedback
            }, false);
        });
    })();
</script>
<?php #endregion js ?>
</body>
</html>