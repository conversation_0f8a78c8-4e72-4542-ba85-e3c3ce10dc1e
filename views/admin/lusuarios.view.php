<?php #region docs
/** @var Usuario[] $usuarios */

use App\classes\Usuario;

#endregion docs
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8">
    <title>Usuarios - <?php echo APP_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Administrar usuarios del sistema">
    <meta name="author" content="">
	
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	

    <!-- ================== BEGIN page-css ================== -->
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <!-- ================== END page-css ================== -->
</head>
<body>
    <!-- BEGIN #app -->
    <div id="app" class="app">
        <!-- BEGIN #header -->
        <?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
        <!-- END #header -->

        <!-- BEGIN #sidebar -->
        <?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
        <!-- END #sidebar -->

        <!-- BEGIN #content -->
        <div id="content" class="app-content">
            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center justify-content-between mb-3">
                <div>
                    <h4 class="mb-1">Usuarios</h4>
                    <p class="text-muted mb-0">Administrar usuarios del sistema</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalUsuario" onclick="abrirModalCrear()">
                    <i class="fa fa-plus me-1"></i> Crear Nuevo
                </button>
            </div>
            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region DATA TABLE ?>
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="usuariosTable" class="table table-bordered table-hover table-hover w-100">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 120px;">Acciones</th>
                                    <th>Username</th>
                                    <th>Nombre</th>
                                </tr>
                            </thead>
                            <tbody class="cursor-pointer">
                                <?php foreach ($usuarios as $usuario): ?>
                                <tr>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-xs btn-primary me-1"
                                                onclick="abrirModalEditar(<?php echo $usuario->getId(); ?>)"
                                                title="Editar usuario">
                                            <i class="fa fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-warning me-1"
                                                onclick="abrirModalCambiarClave(<?php echo $usuario->getId(); ?>)"
                                                title="Cambiar contraseña">
                                            <i class="fa fa-key"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-danger"
                                                onclick="eliminarUsuario(<?php echo $usuario->getId(); ?>)"
                                                title="Desactivar usuario">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                    <td><?php echo htmlspecialchars($usuario->getUsername() ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($usuario->getNombre() ?? ''); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php #endregion DATA TABLE ?>
        </div>
        <!-- END #content -->
    </div>
    <!-- END #app -->

    <?php #region MODAL USUARIO ?>
    <!-- Modal for Create/Edit User -->
    <div class="modal fade" id="modalUsuario" tabindex="-1" aria-labelledby="modalUsuarioLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalUsuarioLabel">Crear Nuevo Usuario</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formUsuario" novalidate>
                    <div class="modal-body">
                        <input type="hidden" id="usuarioId" name="id" value="">
                        <input type="hidden" id="action" name="action" value="crear">

                        <div class="mb-3" id="usernameGroup">
                            <label for="username" class="form-label">
                                Username <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   required maxlength="50" placeholder="Ingrese el username"
                                   style="text-transform: uppercase;">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Se convertirá automáticamente a mayúsculas.</div>
                        </div>

                        <div class="mb-3" id="passwordGroup">
                            <label for="password" class="form-label">
                                Contraseña <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   required minlength="5" placeholder="Ingrese la contraseña">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Mínimo 5 caracteres</div>
                        </div>

                        <div class="mb-3" id="confirmPasswordGroup">
                            <label for="confirm_password" class="form-label">
                                Confirmar Contraseña <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   required minlength="5" placeholder="Confirme la contraseña">
                            <div class="invalid-feedback"></div>
	                        <div class="form-text">Ambas contraseñas deben coincidir.</div>
                        </div>

                        <div class="mb-3">
                            <label for="nombre" class="form-label">
                                Nombre <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="nombre" name="nombre" 
                                   required maxlength="100" placeholder="Ingrese el nombre completo">
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnSubmit">
                            <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
                            Crear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion MODAL USUARIO ?>

    <?php #region MODAL CAMBIAR CLAVE ?>
    <!-- Modal for Change Password -->
    <div class="modal fade" id="modalCambiarClave" tabindex="-1" aria-labelledby="modalCambiarClaveLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCambiarClaveLabel">Cambiar Contraseña</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formCambiarClave" novalidate>
                    <div class="modal-body">
                        <input type="hidden" id="claveUsuarioId" name="id" value="">
                        <input type="hidden" name="action" value="cambiar_clave">

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                Nueva Contraseña <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                   required minlength="5" placeholder="Ingrese la nueva contraseña">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Mínimo 5 caracteres</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_new_password" class="form-label">
                                Confirmar Nueva Contraseña <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="confirm_new_password" name="confirm_password" 
                                   required minlength="5" placeholder="Confirme la nueva contraseña">
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnSubmitClave">
                            <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinnerClave"></span>
                            Cambiar Contraseña
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion MODAL CAMBIAR CLAVE ?>

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

    <!-- DataTables JS -->
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="<?php echo RUTA_ASSETS_ADM ?>plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>

    <script src="<?php echo RUTA_RESOURCES ?>js/lusuarios.js"></script>
    <?php #endregion JS ?>
</body>
</html>
