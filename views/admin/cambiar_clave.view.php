<?php
#region region DOCS & PHP Vars
/**
 * View file for changing the user's own password.
 * Expects the following variables from the controller:
 * @var string $success_text    Success message text (if any).
 * @var string $success_display 'show' or 'hide' for success message visibility.
 * @var string $error_text      Error message text (if any).
 * @var string $error_display   'show' or 'hide' for error message visibility.
 *                              Note: Assumes user is already authenticated (checked in controller).
 */
#endregion DOCS & PHP Vars
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Changed lang to 'es' ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Cambiar Contraseña</title> <?php // Updated Title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<?php #endregion HEAD ?>
	<style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }

        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }

        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }

        /* Optional: Adjust alert visibility based on PHP variables */
        .alert.show {
            display: block;
        }

        .alert.hide {
            display: none;
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Cambiar Mi Contraseña</h4> <?php // Updated Header ?>
				<p class="mb-0 text-muted">Ingrese su nueva contraseña.</p>
			</div>
			<div class="ms-auto">
				<a href="dashboard" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Ir al Dashboard</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
		<?php // Post to the same page to handle the logic ?>
		<form action="cambiar_clave" method="POST" id="change-password-form" novalidate> <?php /* novalidate prevents default browser validation */ ?>
			<?php #region region PANEL PASSWORD DETAILS ?>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Nueva Contraseña</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body">
					
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="nueva_clave" class="form-label">Nueva Contraseña <span class="text-danger">*</span></label>
							<input type="password" class="form-control" id="nueva_clave" name="nueva_clave" required autofocus>
							<div class="invalid-feedback" id="nueva_clave-error">La nueva contraseña es requerida.</div>
						</div>
						<div class="col-md-6">
							<label for="confirm_clave" class="form-label">Confirmar Nueva Contraseña <span class="text-danger">*</span></label>
							<input type="password" class="form-control" id="confirm_clave" name="confirm_clave" required>
							<div class="invalid-feedback" id="confirm_clave-error">Por favor confirme la nueva contraseña.</div>
						</div>
					</div>
				
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Contraseña</button>
				</div>
			</div>
			<?php #endregion PANEL PASSWORD DETAILS ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/cambiar_clave.js"></script>


<?php #endregion JS ?>

</body>
</html>