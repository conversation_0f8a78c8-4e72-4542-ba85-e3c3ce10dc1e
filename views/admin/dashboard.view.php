<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Dashboard</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/dashboard.css" rel="stylesheet" />
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Dashboard</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region CONFIGURATION FORM ?>
		<form action="dashboard" method="POST" id="config-form" novalidate>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Configuración Web</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<div class="row">
						<div class="col-md-6">
							<?php #region region TELEFONO FIELD ?>
							<div class="mb-3">
								<label for="telefono" class="form-label">Teléfono:</label>
								<input type="text"
									   id="telefono"
									   name="telefono"
									   value="<?php echo htmlspecialchars($configData ? $configData->getTelefono() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
									   class="form-control"
									   placeholder="Ej: +1234567890"/>
								<div class="invalid-feedback">
									Por favor, ingrese un teléfono válido.
								</div>
							</div>
							<?php #endregion TELEFONO FIELD ?>

							<?php #region region CORREO FIELD ?>
							<div class="mb-3">
								<label for="correo" class="form-label">Correo Electrónico:</label>
								<input type="email"
									   id="correo"
									   name="correo"
									   value="<?php echo htmlspecialchars($configData ? $configData->getCorreo() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
									   class="form-control"
									   placeholder="Ej: <EMAIL>"/>
								<div class="invalid-feedback">
									Por favor, ingrese un correo electrónico válido.
								</div>
							</div>
							<?php #endregion CORREO FIELD ?>

							<?php #region region FACEBOOK FIELD ?>
							<div class="mb-3">
								<label for="link_facebook" class="form-label">Enlace de Facebook:</label>
								<input type="url"
									   id="link_facebook"
									   name="link_facebook"
									   value="<?php echo htmlspecialchars($configData ? $configData->getLinkFacebook() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
									   class="form-control"
									   placeholder="Ej: https://facebook.com/empresa"/>
								<div class="invalid-feedback">
									Por favor, ingrese una URL válida.
								</div>
							</div>
							<?php #endregion FACEBOOK FIELD ?>
						</div>

						<div class="col-md-6">
							<?php #region region INSTAGRAM FIELD ?>
							<div class="mb-3">
								<label for="link_instagram" class="form-label">Enlace de Instagram:</label>
								<input type="url"
									   id="link_instagram"
									   name="link_instagram"
									   value="<?php echo htmlspecialchars($configData ? $configData->getLinkInstagram() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
									   class="form-control"
									   placeholder="Ej: https://instagram.com/empresa"/>
								<div class="invalid-feedback">
									Por favor, ingrese una URL válida.
								</div>
							</div>
							<?php #endregion INSTAGRAM FIELD ?>

							<?php #region region LINKEDIN FIELD ?>
							<div class="mb-3">
								<label for="link_linkedin" class="form-label">Enlace de LinkedIn:</label>
								<input type="url"
									   id="link_linkedin"
									   name="link_linkedin"
									   value="<?php echo htmlspecialchars($configData ? $configData->getLinkLinkedin() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?>"
									   class="form-control"
									   placeholder="Ej: https://linkedin.com/company/empresa"/>
								<div class="invalid-feedback">
									Por favor, ingrese una URL válida.
								</div>
							</div>
							<?php #endregion LINKEDIN FIELD ?>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<div class="alert alert-info">
								<i class="fa fa-info-circle"></i>
								<strong>Nota:</strong> Todos los campos son opcionales. Puede dejar en blanco los campos que no desee configurar.
							</div>
						</div>
					</div>
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary">
						<i class="fa fa-save fa-fw me-1"></i> Guardar cambios
					</button>
				</div>
			</div>
		</form>
		<?php #endregion CONFIGURATION FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/dashboard.js"></script>

<?php #endregion JS ?>

</body>
</html>