<svg width="120" height="80" viewBox="0 0 120 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="120" height="80" rx="8" fill="#2c3e50"/>
  <rect width="120" height="80" rx="8" fill="url(#gradient)" fill-opacity="0.8"/>
  
  <!-- Play button -->
  <circle cx="60" cy="40" r="18" fill="rgba(255,255,255,0.9)"/>
  <polygon points="54,30 54,50 72,40" fill="#2c3e50"/>
  
  <!-- Video icon -->
  <rect x="25" y="20" width="70" height="40" rx="4" stroke="rgba(255,255,255,0.6)" stroke-width="2" fill="none"/>
  <circle cx="35" cy="30" r="3" fill="rgba(255,255,255,0.6)"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1496D7;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0d7bb8;stop-opacity:0.9" />
    </linearGradient>
  </defs>
</svg>
