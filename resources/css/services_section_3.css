/* Services Section Styles */
.service-categories-grid {
    display: flex;
    flex-direction: column;
    margin-bottom: 50px;
}

.service-category-tabs {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin-bottom: 30px;
    gap: 15px;
}

.category-tab {
    background-color: #fff; /* 3. Turn the background of the categories to white */
    border-radius: 10px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 150px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3); /* 1. Add a more prevalent shadow to each tab */
}

.category-tab:hover, .category-tab.active {
    background-color: #007bff;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.category-tab i {
    color: #007bff;
}

/* Explicitly set h4 text color to white for hovered and active tabs */
.category-tab:hover h4,
.category-tab:hover i,
.category-tab.active h4,
.category-tab.active i {
    color: white !important;
}

.tab-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.category-tab h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.service-category-content {
    position: relative;
}

.category-content {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.category-content.active {
    display: block;
}

.category-header {
    text-align: center;
    margin-bottom: 30px;
}

.category-header h3 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.category-description {
    font-size: 16px;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.service-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.service-number {
    background-color: #007bff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.service-details h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Core layout for service category tabs */
.service-category-tabs {
    display: flex;
    flex-wrap: nowrap; /* Ensures items stay in a single line */
    padding-left: 0;   /* Override default ul padding */
    list-style-type: none; /* Remove list bullets */
    width: 100%;       /* Make the ul take full width of its container */
    margin-top: 20px;  /* Add some space above the tabs */
    margin-bottom: 30px; /* Space below the tabs */
}

.service-category-tabs .category-tab {
    flex-grow: 1;   /* Each tab will take up an equal amount of space */
    flex-shrink: 1; /* Allow tabs to shrink if needed, maintaining equal proportion */
    flex-basis: 0;  /* Initial size is 0, growth is based on flex-grow */

    display: flex;
    flex-direction: column; /* Stack icon and text vertically */
    align-items: center;   /* Center icon and text horizontally */
    justify-content: center; /* Center icon and text vertically within the tab */

    padding: 15px 10px; /* Padding within each tab (vertical horizontal) */
    box-sizing: border-box; /* Include padding and border in width calculation */
    text-align: center; /* Ensures text within h4 is centered */
    cursor: pointer;    /* Indicate interactivity */

    /* Minimal styling to not heavily override existing theme */
    /* border: 1px solid #ddd; /* Uncomment for visible separation */
    transition: background-color 0.2s ease; /* For hover effects if any */
}

/* Spacing between tabs */
.service-category-tabs .category-tab + .category-tab {
    margin-left: 10px; /* Adjust as needed for spacing between tabs */
}

/* Optional: Basic hover effect if not already present from other CSS */
/*
.service-category-tabs .category-tab:hover {
    background-color: #f5f5f5;
}
*/

/* Ensure active tab stands out, complementing existing styles from services_section.css */
.service-category-tabs .category-tab.active {
    font-weight: bold; /* Example: make active tab text bold */
    /* You can add other specific active styles here if needed,
        e.g., a distinct border-bottom or background,
        if not sufficiently handled by services_section.css */
}

.category-tab .tab-icon {
    margin-bottom: 8px; /* Space between icon and text */
    font-size: 1.8em;   /* Example icon size, adjust as needed */
}

.category-tab h4 {
    font-size: 0.9em; /* Adjusted for better fit if category names are long */
    margin: 0;        /* Remove default h4 margin */
    line-height: 1.2; /* Adjust line height for readability */
}

/* Mobile Responsive Styles */
@media (max-width: 991px) {
    /* Adjust service section padding for mobile */
    #serviciostec.service-details-wrapper {
        padding: 40px 0;
    }

    /* Make category tabs more touch-friendly */
    .service-category-tabs {
        flex-wrap: wrap;
        gap: 10px;
    }

    .service-category-tabs .category-tab {
        flex-basis: calc(50% - 10px); /* Two tabs per row on medium screens */
        margin-left: 0;
        min-height: 80px;
    }

    .service-category-tabs .category-tab + .category-tab {
        margin-left: 0; /* Remove left margin on mobile */
    }

    /* Adjust service items grid for tablets */
    .service-items {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    /* Adjust category header for better mobile display */
    .category-header h3 {
        font-size: 24px;
    }
}

@media (max-width: 767px) {
    /* Further adjustments for smaller screens */
    .service-category-tabs .category-tab {
        flex-basis: 100%; /* Full width tabs on small screens */
        margin-bottom: 10px;
        padding: 12px 8px;
    }

    /* Single column layout for service items on mobile */
    .service-items {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* Make service items more compact on mobile */
    .service-item {
        padding: 15px;
    }

    /* Adjust font sizes for mobile */
    .category-header h3 {
        font-size: 22px;
    }

    .service-details h5 {
        font-size: 16px;
    }

    /* Improve tab icon visibility on mobile */
    .category-tab .tab-icon {
        font-size: 1.5em;
        margin-bottom: 5px;
    }

    .category-tab h4 {
        font-size: 0.85em;
    }

    /* Ensure proper spacing in the service section */
    #serviciostec.service-details-wrapper {
        padding: 30px 0;
    }

    .service-categories-grid {
        margin-bottom: 30px;
    }
}

@media (max-width: 480px) {
    /* Specific adjustments for very small screens */
    .service-category-tabs {
        gap: 8px;
    }

    .category-tab {
        min-width: 100px;
        padding: 10px 8px;
    }

    /* Further reduce font sizes */
    .category-header h3 {
        font-size: 20px;
    }

    /* Make service number smaller on very small screens */
    .service-number {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    /* Ensure proper touch targets */
    .service-item {
        min-height: 60px;
    }
}

.service-categories-grid h4, .service-category-content h5{
    color: #252222 !important;
}