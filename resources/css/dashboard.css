/* Dashboard Configuration Form Styles */

/* Configuration Panel Styling */
#config-form .panel {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

#config-form .panel-heading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

#config-form .panel-title {
    font-weight: 600;
    font-size: 1.1rem;
}

/*#config-form .panel-body {
    padding: 25px;
    background-color: #fff;
}*/

/* Form Field Styling */
/*#config-form .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}*/

/*#config-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}*/

#config-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

#config-form .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

#config-form .form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Validation Feedback */
#config-form .invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 5px;
    font-size: 0.875rem;
    color: #dc3545;
}

#config-form .valid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 0.875rem;
    color: #28a745;
}

/* Info Alert Styling */
#config-form .alert-info {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #0277bd;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
}

#config-form .alert-info i {
    margin-right: 8px;
}

/* Submit Button Styling */
/*#config-form .panel-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
}*/

#config-form .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 25px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#config-form .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

#config-form .btn-primary:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    #config-form .panel-body {
        padding: 20px 15px;
    }

    #config-form .panel-footer {
        padding: 15px;
        text-align: center;
    }

    #config-form .btn-primary {
        width: 100%;
    }
}

/* Loading State */
#config-form.loading .btn-primary {
    opacity: 0.6;
    cursor: not-allowed;
}

#config-form.loading .btn-primary:hover {
    transform: none;
    box-shadow: none;
}

/* Field Icons (if needed) */
#config-form .input-group-text {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    color: #6c757d;
}

/* Smooth Animations */
#config-form .form-control,
#config-form .btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus Ring for Accessibility */
#config-form .form-control:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}