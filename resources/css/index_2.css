#p_elegir_aliado_estrategico {
    text-align: justify;
}

@media (min-width: 1024px) {
    #aliado_estrategico .pilar-titulo{
        margin-left: 20px;
    }
}

@media (max-width: 1024px) {
    #aliado_estrategico .pilar-titulo{
        margin-bottom: 20px;
    }

    .process__widget.text-center {
        margin-top: 20px;
    }

    #aliado_estrategico .pilar-parrafo{
        font-size: 17px;
        height   : 430px !important;
    }
}

/* Partners Section Styles */
#partners {
    background-color: #f8f9fa;
}

.partners-carousel-container {
    overflow: hidden;
    width: 100%;
    height: 120px;
    position: relative;
    background: linear-gradient(90deg, rgba(248,249,250,1) 0%, rgba(248,249,250,0) 10%, rgba(248,249,250,0) 90%, rgba(248,249,250,1) 100%);
}

.partners-scroll-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    width: calc(200% + 40px); /* Double width plus gap compensation for seamless loop */
    animation: partners-scroll 40s linear infinite;
    will-change: transform;
}

.partner-logo {
    flex: 0 0 auto;
    width: 200px;
    height: 80px;
    margin-right: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.partner-logo:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.partner-content {
    text-align: center;
    padding: 10px;
}

.partner-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.partner-logo:hover .partner-name {
    color: #007bff;
}

/* Infinite scroll animation */
@keyframes partners-scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .partners-carousel-container {
        height: 100px;
    }

    .partner-logo {
        width: 160px;
        height: 60px;
        margin-right: 30px;
    }

    .partner-name {
        font-size: 12px;
    }

    .partners-scroll-wrapper {
        animation-duration: 30s; /* Faster on mobile */
    }
}

@media (max-width: 480px) {
    .partners-carousel-container {
        height: 80px;
    }

    .partner-logo {
        width: 140px;
        height: 50px;
        margin-right: 20px;
    }

    .partner-name {
        font-size: 11px;
    }

    .partners-scroll-wrapper {
        animation-duration: 25s; /* Even faster on small mobile */
    }
}
