/* ========================================
   PROYECTOS PAGE STYLES
   ======================================== */

/* Projects Section */
.projects-section {
    background-color: #f8f9fa;
    padding: 60px 0;
}

.section-padding{
    padding: 20px 0px !important;
}

/* Project Card */
.project-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Project Image */
.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

/* Project Overlay */
.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(111, 164, 196, 0.9), rgba(20, 150, 215, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-overlay-content {
    text-align: center;
    color: white;
    padding: 20px;
}

.project-overlay-content h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.3;
}

.project-overlay-content p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.project-overlay-content .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    font-weight: 500;
    padding: 8px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.project-overlay-content .btn:hover {
    background: white;
    color: var(--azul-oscuro-empresarial, #1496D7);
}

/* Project Info */
.project-info {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Client Logo */
.client-logo {
    text-align: center;
    margin-bottom: 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-logo img {
    max-height: 50px;
    max-width: 120px;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.project-card:hover .client-logo img {
    filter: grayscale(0%);
}

/* Project Details */
.project-details {
    flex-grow: 1;
}

.project-title {
    font-size: 1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    line-height: 1.3;
}

.client-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--azul-oscuro-empresarial, #1496D7);
    margin-bottom: 10px;
}

.client-name i {
    margin-right: 8px;
    color: var(--azul-claro-empresarial, #6FA4C4);
}

.project-location {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 15px;
}

.project-location i {
    margin-right: 8px;
    color: var(--azul-claro-empresarial, #6FA4C4);
}

.project-description {
    font-size: 0.9rem;
    color: #5a6c7d;
    line-height: 1.6;
    text-align: justify;
}

/* CTA Section */
.cta-section {
    padding: 60px 0;
}

.cta-content h3 {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 1.1rem;
    color: #7f8c8d;
    line-height: 1.6;
}

.cta-section .btn {
    background: var(--azul-oscuro-empresarial, #1496D7);
    border: none;
    padding: 15px 30px;
    font-weight: 500;
    border-radius: 30px;
    transition: all 0.3s ease;
}

.cta-section .btn:hover {
    background: var(--azul-claro-empresarial, #6FA4C4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(20, 150, 215, 0.3);
}

/* No Projects State */
.no-projects {
    padding: 60px 20px;
}

.no-projects i {
    color: #bdc3c7;
}

.no-projects h4 {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.no-projects p {
    color: #95a5a6;
    font-size: 1.1rem;
}

/* ========================================
   GALLERY ITEMS STYLES
   ======================================== */

/* Gallery Items */
.gallery-item {
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.gallery-item:hover {
    opacity: 0.8;
}

/* Hidden gallery items */
.hidden-gallery {
    position: absolute;
    left: -9999px;
    top: -9999px;
}

/* Video thumbnail placeholder (if needed) */
.gallery-video {
    position: relative;
}

.gallery-video::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-video:hover::before {
    opacity: 1;
}

.gallery-video::after {
    content: '▶';
    position: absolute;
    top: 50%;
    left: 52%;
    transform: translate(-50%, -50%);
    color: var(--azul-oscuro-empresarial, #1496D7);
    font-size: 20px;
    z-index: 2;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-video:hover::after {
    opacity: 1;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Tablet Styles */
@media (max-width: 991.98px) {
    .project-image {
        height: 220px;
    }

    .project-title {
        font-size: 1.2rem;
    }

    .cta-content h3 {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 20px;
    }

    .cta-section .btn {
        margin-top: 20px;
    }
}

/* Mobile Styles */
@media (max-width: 767.98px) {
    .projects-section {
        padding: 40px 0;
    }

    .project-image {
        height: 200px;
    }

    .project-info {
        padding: 20px;
    }

    .project-title {
        font-size: 1.1rem;
    }

    .client-name {
        font-size: 0.9rem;
    }

    .project-description {
        font-size: 0.85rem;
    }

    .cta-section {
        padding: 40px 0;
    }

    .cta-content h3 {
        font-size: 1.5rem;
        text-align: center;
    }

    .cta-content p {
        font-size: 1rem;
        text-align: center;
    }

    .project-overlay-content h5 {
        font-size: 1rem;
    }

    .project-overlay-content p {
        font-size: 0.8rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 575.98px) {
    .project-card {
        margin-bottom: 30px;
    }

    .project-image {
        height: 180px;
    }

    .client-logo {
        height: 50px;
        margin-bottom: 15px;
    }

    .client-logo img {
        max-height: 40px;
        max-width: 100px;
    }

    .project-info {
        padding: 15px;
    }

    .project-title {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .section-title h2 {
        font-size: 1.8rem;
    }
}

/* ========================================
   MAGNIFIC POPUP CUSTOMIZATION
   ======================================== */

/* Custom styles for the lightbox */
.mfp-bg {
    background: rgba(0, 0, 0, 0.9);
}

.mfp-image-holder .mfp-content {
    max-width: 90%;
}

.mfp-title {
    color: white;
    font-size: 1.1rem;
    padding: 10px 0;
    text-align: center;
}

/* ========================================
   VIDEO GALLERY STYLES
   ======================================== */

/* Media container in lightbox */
.media-container {
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

/* Image content in lightbox */
.gallery-image-content {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

/* ========================================
   FANCYBOX CUSTOMIZATION
   ======================================== */

/* Custom Fancybox styling to match site theme */
.fancybox__backdrop {
    background: rgba(0, 0, 0, 0.95);
}

.fancybox__toolbar {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
}

.fancybox__button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.fancybox__button:hover {
    background: var(--azul-oscuro-empresarial, #1496D7);
    border-color: var(--azul-oscuro-empresarial, #1496D7);
}

.fancybox__nav {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

.fancybox__nav:hover {
    background: var(--azul-oscuro-empresarial, #1496D7);
}

.fancybox__nav svg {
    width: 24px;
    height: 24px;
    stroke: white;
    stroke-width: 2;
    fill: none;
}

.fancybox__caption {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    font-size: 1rem;
    padding: 20px;
    text-align: center;
}

/* Video player in Fancybox */
.fancybox__html5video {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

/* Loading spinner */
.fancybox__spinner {
    border-color: var(--azul-oscuro-empresarial, #1496D7);
}

/* Counter styling */
.fancybox__counter {
    color: white;
    font-weight: 500;
}

/* Video container in lightbox */
.video-container {
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

/* Video player styling */
.gallery-video-player {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    background: #000;
}

/* ========================================
   FANCYBOX THUMBNAILS CUSTOMIZATION
   ======================================== */

/* Thumbnail container */
.fancybox__thumbs {
    background: rgba(0, 0, 0, 0.9);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Individual thumbnail styling */
.fancybox__thumb {
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.fancybox__thumb:hover {
    opacity: 1;
    border-color: var(--azul-oscuro-empresarial, #1496D7);
    transform: scale(1.05);
}

.fancybox__thumb.is-selected {
    opacity: 1;
    border-color: var(--azul-oscuro-empresarial, #1496D7);
    box-shadow: 0 0 10px rgba(20, 150, 215, 0.5);
}

/* Thumbnail images */
.fancybox__thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.fancybox__thumb:hover img {
    transform: scale(1.1);
}

/* Video placeholder thumbnail styling */
.fancybox__thumb img[src*="video-placeholder"] {
    background: linear-gradient(135deg, #2c3e50 0%, #1496D7 100%);
    border-radius: 4px;
}

/* Thumbnail navigation buttons */
.fancybox__thumbs .carousel__button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.fancybox__thumbs .carousel__button:hover {
    background: var(--azul-oscuro-empresarial, #1496D7);
    border-color: var(--azul-oscuro-empresarial, #1496D7);
}

/* Responsive thumbnail adjustments */
@media (max-width: 768px) {
    .fancybox__thumbs {
        height: 80px;
    }

    .fancybox__thumb {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 480px) {
    .fancybox__thumbs {
        height: 70px;
    }

    .fancybox__thumb {
        width: 50px;
        height: 50px;
    }
}

/* Video controls styling */
.gallery-video-player::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.8);
}

.gallery-video-player::-webkit-media-controls-play-button,
.gallery-video-player::-webkit-media-controls-volume-slider,
.gallery-video-player::-webkit-media-controls-timeline,
.gallery-video-player::-webkit-media-controls-current-time-display,
.gallery-video-player::-webkit-media-controls-time-remaining-display {
    color: white;
}

/* Loading state for videos */
.gallery-video-player[data-loading="true"] {
    background: #000 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="none" stroke="white" stroke-width="2" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/></circle></svg>') center center no-repeat;
    background-size: 50px 50px;
}

/* Video error state */
.video-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    z-index: 10;
}

.video-error i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #e74c3c;
}

.video-error p {
    margin: 0;
    font-size: 1rem;
}

/* Responsive video styles */
@media (max-width: 768px) {
    .video-container {
        max-width: 95vw;
        max-height: 70vh;
    }

    .gallery-video-player {
        max-height: 70vh;
    }
}

@media (max-width: 480px) {
    .video-container {
        max-width: 98vw;
        max-height: 60vh;
    }

    .gallery-video-player {
        max-height: 60vh;
    }
}

/* ========================================
   LOADING STATES & ANIMATIONS
   ======================================== */

.project-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.project-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--azul-oscuro-empresarial, #1496D7);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

.block-contents .section-title h2 {
    font-size: 2rem !important;
}