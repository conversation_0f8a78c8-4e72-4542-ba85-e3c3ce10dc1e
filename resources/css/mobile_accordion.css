/* Mobile Accordion Styles for Service Categories */

/* Hide desktop version on mobile, show mobile version */
@media (max-width: 767px) {
    .desktop-only {
        display: none !important;
    }
    
    .mobile-only {
        display: block !important;
    }
}

/* Hide mobile version on desktop, show desktop version */
@media (min-width: 768px) {
    .mobile-only {
        display: none !important;
    }
    
    .desktop-only {
        display: flex !important;
        flex-direction: column;
    }
}

/* Mobile Accordion Styles */
.service-categories-accordion {
    display: none; /* Hidden by default, shown only on mobile via media query */
    margin-bottom: 50px;
}

.accordion-item {
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.accordion-item:hover {
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
}

.accordion-header {
    background-color: #fff;
    padding: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
    border-radius: 10px 10px 0 0; /* Ensure top border radius is preserved */
    position: relative;
    z-index: 1;
}

.accordion-header:hover,
.accordion-header.active {
    background-color: #007bff;
    color: white;
}

.accordion-header .tab-icon {
    font-size: 24px;
    margin-right: 15px;
    color: #007bff;
    transition: color 0.3s ease;
}

.accordion-header:hover .tab-icon,
.accordion-header.active .tab-icon {
    color: white;
}

.accordion-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    flex-grow: 1;
    color: #333;
    transition: color 0.3s ease;
}

.accordion-header:hover h4,
.accordion-header.active h4 {
    color: white !important;
}

.accordion-toggle {
    font-size: 16px;
    color: #007bff;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.accordion-header:hover .accordion-toggle,
.accordion-header.active .accordion-toggle {
    color: white;
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-out, padding 0.4s ease-out, opacity 0.3s ease-out;
    background-color: #f8f9fa;
    padding: 0 20px;
    opacity: 0;
}

.accordion-content.active {
    max-height: 2000px; /* Increased to accommodate larger content */
    padding: 20px;
    border-top: 1px solid rgba(0,0,0,0.1);
    opacity: 1;
}

/* When accordion is closed, ensure header has full border radius */
.accordion-header:not(.active) {
    border-radius: 10px;
}

/* Service items styling for mobile accordion */
.accordion-content .service-items {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 10px;
}

.accordion-content .service-item {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    border-left: 3px solid transparent;
}

.accordion-content .service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 3px solid #007bff;
}

.accordion-content .service-number {
    background: linear-gradient(135deg, #007bff, #00c6ff);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
    font-size: 14px;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.accordion-content .service-item:hover .service-number {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.accordion-content .service-details h5 {
    margin: 0;
    font-size: 15px;
    color: #333;
    transition: all 0.3s ease;
    line-height: 1.3;
}

.accordion-content .service-item:hover .service-details h5 {
    color: #007bff;
}

/* Smooth animation for accordion toggle icon */
.accordion-header .accordion-toggle i {
    transition: transform 0.3s ease;
}

.accordion-header.active .accordion-toggle i {
    transform: rotate(180deg);
}

/* Additional responsive adjustments for very small screens */
@media (max-width: 480px) {
    .accordion-header {
        padding: 15px;
    }
    
    .accordion-header .tab-icon {
        font-size: 20px;
        margin-right: 10px;
    }
    
    .accordion-header h4 {
        font-size: 14px;
    }
    
    .accordion-content {
        padding: 0 15px;
    }

    .accordion-content.active {
        padding: 15px;
        max-height: 2000px; /* Ensure mobile also has adequate max-height */
    }
    
    .accordion-content .service-item {
        padding: 12px;
    }
    
    .accordion-content .service-number {
        width: 30px;
        height: 30px;
        font-size: 12px;
        margin-right: 12px;
    }
    
    .accordion-content .service-details h5 {
        font-size: 14px;
    }
}

/* Ensure proper spacing and layout */
.service-categories-accordion .accordion-item:last-child {
    margin-bottom: 0;
}

/* Add a subtle gradient border to active accordion items */
.accordion-header.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    border-radius: 10px 10px 0 0; /* Match the header border radius */
    z-index: 2;
}
