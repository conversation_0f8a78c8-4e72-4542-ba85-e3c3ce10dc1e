.policy-container {
    line-height: 1.6;
    margin: 20px;
    color: #333;
}
.policy-container h2 {
    color: #0056b3;
    padding-bottom: 5px;
}
.policy-container p {
    margin-bottom: 15px;
    text-align: justify; /* Added justify */
}
.policy-container ul, .policy-container ol {
    margin-bottom: 20px;
}
.policy-container ul {
    list-style-type: disc !important;
}
.policy-container ol {
    list-style-type: decimal !important;
}
.policy-container li {
    margin-bottom: 0px; /* Remove space between li */
    list-style: initial !important;
    position: static !important;
    margin-left: 20px;
    padding: 0; /* Remove padding */
}
.policy-container strong {
    font-weight: bold;
}
.policy-container .container {
    max-width: 800px;
    margin: auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}
.policy-container .contact-info {
    background-color: #e0f7fa;
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
}
.policy-container .contact-info a {
    color: #007bff;
    text-decoration: none;
}
.policy-container .contact-info a:hover {
    text-decoration: underline;
}
.policy-container .nested-list {
    padding-left: 20px;
    list-style-type: circle !important;
}

/* Estilos del template original */
.policy-container ul li {
    list-style-type: disc; /* Ensure standard bullets for ul */
    margin-left: 20px;
    margin-bottom: 0.5rem; /* Add some space between list items */
}

.policy-container ul.nested-list li { /* Estilo para listas anidadas si se usan */
    list-style-type: circle;
    margin-left: 40px;
}

.policy-container ul ul { /* Margen para sub-listas */
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.policy-container .contact-info strong {
    display: inline-block;
    min-width: 150px; /* Adjust as needed for alignment */
}

.policy-container h3 {
    margin-top: 1.5rem; /* Add space above headings */
    margin-bottom: 0.75rem;
    color: #333; /* Darker heading color */
}

.policy-container h2 {
    margin-bottom: 1rem;
    color: #0d6efd; /* Primary color for main title */
}

.policy-container p {
    line-height: 1.6; /* Improve readability */
    margin-bottom: 1rem;
}

/*.policy-container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}*/

.policy-container .fechas strong {
    display: inline-block;
    min-width: 230px;
}
