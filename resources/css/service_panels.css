/* Service Panels Styling - Overrides and enhances services_section_3.css */
.category-content {
    background-color: #f8f9fa;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    padding: 25px;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.category-content:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #007bff, #00c6ff);
}

/* Add a subtle hover effect to the entire panel */
.category-content:hover {
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    transform: translateY(-5px);
}

/* Make sure only active content is displayed */
.category-content {
    display: none;
}

.category-content.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.category-header {
    margin-bottom: 20px;
    text-align: center;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    position: relative;
}

/* Add decorative elements to the header */
.category-header:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    border-radius: 3px;
}

.category-header h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
    text-transform: none;
    font-weight: 600;
}

.category-description {
    color: #666;
    font-size: 16px;
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 10px;
}

.service-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    border-left: 3px solid transparent;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-left: 3px solid #007bff;
}

/* Style the service number */
.service-number {
    background: linear-gradient(135deg, #007bff, #00c6ff);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.service-item:hover .service-number {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.4);
}

.service-details h5 {
    margin: 0;
    font-size: 16px;
    color: #333;
    transition: all 0.3s ease;
}

.service-item:hover .service-details h5 {
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .category-content {
        padding: 20px 15px;
    }

    .service-items {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 767px) {
    .category-content {
        padding: 15px 10px;
    }

    .service-items {
        grid-template-columns: 1fr;
    }

    .category-header h3 {
        font-size: 22px;
    }

    .service-number {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}
