/* Services Admin Page Styles */

/* Modal form enhancements */
/*#modalServicio .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}*/

#modalServicio .text-danger {
    color: #dc3545 !important;
}

#modalServicio .form-text {
    font-size: 0.875em;
    color: #6c757d;
}

/* Loading spinner */
#loadingSpinner {
    width: 1rem;
    height: 1rem;
}

/* Badge styling for priority */
.badge.bg-primary {
    background-color: #0d6efd !important;
}

/* Category icon spacing */
.table td i.me-2 {
    margin-right: 0.5rem !important;
}

/* Validation feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Dark mode adjustments */
/*.dark-mode #modalServicio .modal-content {
    background-color: #2d3748;
    border-color: #4a5568;
}

.dark-mode #modalServicio .form-control,
.dark-mode #modalServicio .form-select {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
}

.dark-mode #modalServicio .form-control:focus,
.dark-mode #modalServicio .form-select:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
    box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25);
}

.dark-mode .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .text-muted {
    color: #a0aec0 !important;
}*/
