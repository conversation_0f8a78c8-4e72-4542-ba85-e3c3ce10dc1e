#estrategias .single-icon-circle-item{
    background-color: white !important;
    padding: 20px !important;
    border-radius: 10px !important;
    height: 240px;
    cursor: pointer;
}

#estrategias .single-icon-circle-item:hover{
    background-color: var(--azul-oscuro-empresarial) !important;
    color: white !important;
}

#estrategias .single-icon-circle-item:hover h3,
#estrategias .single-icon-circle-item:hover .icon{
    color: white !important;
}

#estrategias .single-icon-circle-item:hover .icon{
    background: var(--azul-oscuro-empresarial) !important;
}

#estrategias .single-icon-circle-item:hover .icon::before{
    border: 5px solid white !important;
}

#estrategias .single-icon-circle-item .contents p{
    text-align: justify !important;
}

#estrategias h3{
    font-size: 19px !important;
}

/* ONLY MOBILE */
@media (max-width: 1024px){
    #estrategias .single-icon-circle-item {
        height: 460px;
    }

    .single-icon-circle-item {
        text-align: center;
    }

    /* Horizontally center the .icon div (and .contents div) within .single-icon-circle-item */
    #estrategias .single-icon-circle-item {
        display: flex;
        flex-direction: column; /* Stack .icon and .contents vertically */
        align-items: center;    /* Horizontally center .icon and .contents blocks */
    }

    /* Vertically (and horizontally) center the <i> tag within the .icon div */
    #estrategias .single-icon-circle-item .icon {
        display: flex;
        align-items: center;    /* Vertically center content (the <i> tag) */
        justify-content: center;/* Horizontally center content (the <i> tag) */
    }
}