
:root {
  --purple: #6f42c1 !important;
}

body{
    font-family: "Roboto", serif;
    font-weight: 500;
    font-style: normal;
}

.ps-8 {
    padding-left: 8rem !important;
}

.ps-9 {
    padding-left: 9rem !important;
}

.ps-10 {
    padding-left: 10rem !important;
}

.centered {
    display: flex;
    justify-content: center; /* Centers horizontally */
    align-items: center;    /* Centers vertically */
}

.text-justify{
    text-align: justify;
}

#pricing ul.features {
    height: 340px;
}

.price-figure .price-number {
    line-height: 1 !important;
}

.pricing-table .price .price-number {
    font-size: 1.55rem !important;
    color: white !important;
}

.pricing-table .price {
    background: var(--purple) !important;
}

ul.ingresos_extras ::marker{
    color: var(--purple) !important;
}

.text-theme {
    color: var(--purple) !important;
}

.header.navbar-default .navbar-nav .nav-item .nav-link.active, .header.navbar-default .navbar-nav .nav-item .nav-link:hover {
    color: var(--purple) !important;
}

#planes .pricing-table .features {
    background: white !important;
}

#planes .pricing-table {
    display: block !important;
}

#planes .card-body {
    padding: 1.73rem 1rem !important;
}

.purple-text{
    color: var(--purple) !important;
}

.mt-10{
    margin-top: 10px !important;
}

.text-white{
    color: white !important;
}

/* BEGIN datepicker */
body .datepicker.dropdown-menu {
    min-width: 300px !important;
}
.datepicker{
    padding: .4375rem .75rem !important;
}
/* END datepicker */

/* Custom styles for disabled select */
select:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    cursor: not-allowed;
}

/* Style for input group with disabled select */
.input-group select:disabled + button.btn-outline-success {
    background-color: #f8f9fa;
    border-color: #28a745;
    color: #28a745;
    opacity: 0.65;
}

/* START Style for date icon */
.date-icon {
    cursor: pointer;
    background-color: #6c757d; /* Using the project's purple color */
    border-color: #6c757d;
    color: #fff;
    transition: background-color 0.2s ease;
}

.date-icon:hover {
    background-color: #51585e; /* Darker shade for hover state */
}
/* START Style for date icon */

/* Div que rodea el logo del topbar */
.app-header .navbar-brand{
    padding: 2px 20px !important;
}