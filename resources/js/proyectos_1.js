/**
 * PROYECTOS PAGE JAVASCRIPT
 * Handles interactive functionality for the projects page with Fancybox
 */

$(document).ready(function() {

    // Initialize page functionality
    initializeProjects();

    /**
     * Initialize all project-related functionality
     */
    function initializeProjects() {
        initializeFancybox();
        initializeGalleryButtons();
        initializeAnimations();

        console.log('Proyectos page initialized successfully with Fancybox');
    }

    /**
     * Initialize Fancybox for mixed media galleries
     */
    function initializeFancybox() {
        // Initialize Fancybox with mixed media support
        Fancybox.bind("[data-fancybox]", {
            // Gallery settings
            Carousel: {
                infinite: false,
                Navigation: {
                    prevTpl: '<button class="fancybox__nav fancybox__nav--prev" title="Anterior"><svg><path d="M15 4l-8 8 8 8"></path></svg></button>',
                    nextTpl: '<button class="fancybox__nav fancybox__nav--next" title="Siguiente"><svg><path d="M9 4l8 8-8 8"></path></svg></button>'
                }
            },

            // Enable thumbnails
            Thumbs: {
                autoStart: true,
                minScreenHeight: 700,
                axis: "x"
            },

            // Toolbar settings
            Toolbar: {
                display: {
                    left: ["infobar"],
                    middle: [],
                    right: ["slideshow", "thumbs", "zoom", "fullscreen", "close"]
                }
            },

            // Image settings
            Images: {
                zoom: true,
                protected: true
            },

            // Video settings
            Html: {
                video: {
                    tpl: '<video class="fancybox__html5video" playsinline controls controlsList="nodownload" poster="{{poster}}">' +
                         '<source src="{{src}}" type="{{format}}" />' +
                         'Tu navegador no soporta la reproducción de videos.' +
                         '</video>',
                    format: "video/mp4",
                    autoplay: false
                }
            },

            // Callbacks
            on: {
                init: function() {
                    console.log('Fancybox gallery initialized');
                },
                reveal: function(fancybox, slide) {
                    // Auto-play videos when they appear
                    if (slide.type === 'html' && slide.$content) {
                        const video = slide.$content.find('video')[0];
                        if (video) {
                            video.muted = true;
                            video.play().catch(function(error) {
                                console.log('Video autoplay failed:', error);
                            });
                        }
                    }
                },
                close: function() {
                    // Pause all videos when gallery closes
                    $('video').each(function() {
                        this.pause();
                    });
                }
            }
        });
    }

    /**
     * Handle gallery button clicks
     */
    function initializeGalleryButtons() {
        $('.view-gallery').on('click', function(e) {
            e.preventDefault();

            const projectId = $(this).data('project-id');
            const projectCard = $(this).closest('.project-card');

            // Find the first gallery item for this project and trigger Fancybox
            const firstGalleryItem = projectCard.find('[data-fancybox="gallery-' + projectId + '"]').first();

            if (firstGalleryItem.length) {
                // Trigger Fancybox gallery with thumbnail support
                Fancybox.show(
                    projectCard.find('[data-fancybox="gallery-' + projectId + '"]').toArray().map(function(el) {
                        return {
                            src: el.href,
                            type: el.href.match(/\.(mp4|webm|ogg)$/i) ? 'html5video' : 'image',
                            caption: el.getAttribute('data-caption'),
                            thumb: el.getAttribute('data-thumb')
                        };
                    }),
                    {
                        startIndex: 0,
                        Thumbs: {
                            autoStart: true
                        }
                    }
                );
            } else {
                console.warn('No gallery items found for project ID:', projectId);
                if (typeof showNotification === 'function') {
                    showNotification('No hay contenido multimedia disponible para este proyecto', 'warning');
                }
            }
        });
    }

    /**
     * Initialize scroll animations and other effects
     */
    function initializeAnimations() {
        // Add smooth hover effects for project cards
        $('.project-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Initialize WOW.js if available
        if (typeof WOW !== 'undefined') {
            new WOW().init();
        }

        // Lazy loading for images (if needed)
        initializeLazyLoading();

        // Initialize video error handling
        initializeVideoErrorHandling();
    }

    /**
     * Initialize video error handling
     */
    function initializeVideoErrorHandling() {
        // Handle video loading errors
        $(document).on('error', '.gallery-video-player', function() {
            console.warn('Video failed to load:', $(this).find('source').attr('src'));
            $(this).closest('.video-container').append(
                '<div class="video-error">' +
                    '<i class="fas fa-exclamation-triangle"></i>' +
                    '<p>Error al cargar el video</p>' +
                '</div>'
            );
        });

        // Handle video loading success
        $(document).on('loadedmetadata', '.gallery-video-player', function() {
            $(this).removeAttr('data-loading');
        });

        // Set loading state when video starts loading
        $(document).on('loadstart', '.gallery-video-player', function() {
            $(this).attr('data-loading', 'true');
        });
    }

    /**
     * Initialize lazy loading for project images
     */
    function initializeLazyLoading() {
        // Simple lazy loading implementation
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        // Observe all project images
        document.querySelectorAll('.project-image img').forEach(img => {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
    }

    /**
     * Show notification to user
     */
    function showNotification(message, type = 'info') {
        // Use SweetAlert if available
        if (typeof swal !== 'undefined') {
            swal({
                title: type === 'warning' ? 'Atención' : 'Información',
                text: message,
                icon: type,
                button: 'Entendido'
            });
        } else {
            // Fallback to alert
            alert(message);
        }
    }

    /**
     * Handle responsive behavior
     */
    function handleResponsive() {
        const windowWidth = $(window).width();

        // Adjust overlay behavior on mobile
        if (windowWidth <= 768) {
            $('.project-overlay').addClass('mobile-overlay');
        } else {
            $('.project-overlay').removeClass('mobile-overlay');
        }
    }

    // Handle window resize
    $(window).on('resize', function() {
        handleResponsive();
    });

    // Initial responsive check
    handleResponsive();

    /**
     * Smooth scrolling for internal links
     */
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();

        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 800);
        }
    });

});