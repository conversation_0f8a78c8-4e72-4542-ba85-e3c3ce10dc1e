// Global variables
let dataTable;

// Initialize DataTable
$(document).ready(function() {
    dataTable = $('#data-table-servicios').DataTable({
        responsive: true,
        language: {
            "sProcessing": "Procesando...",
            "sLengthMenu": "Mostrar _MENU_ registros",
            "sZeroRecords": "No se encontraron resultados",
            "sEmptyTable": "Ningún dato disponible en esta tabla",
            "sInfo": "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
            "sInfoEmpty": "Mostrando registros del 0 al 0 de un total de 0 registros",
            "sInfoFiltered": "(filtrado de un total de _MAX_ registros)",
            "sSearch": "Buscar:",
            "oPaginate": {
                "sFirst": "Primero",
                "sLast": "Último",
                "sNext": "Siguiente",
                "sPrevious": "Anterior"
            }
        },
        order: [[1, 'asc'], [3, 'asc']], // Order by category, then priority
        columnDefs: [
            { orderable: false, targets: 0 } // Disable ordering on Actions column
        ]
    });
});

// Open create modal
function abrirModalCrear() {
    $('#modalServicioLabel').text('Crear Nuevo Servicio');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#servicioId').val('');
    $('#formServicio')[0].reset();
    $('#formServicio').removeClass('was-validated');
    $('.invalid-feedback').text('');
}

// Open edit modal
function abrirModalEditar(id) {
    $('#modalServicioLabel').text('Editar Servicio');
    $('#action').val('editar');
    $('#btnSubmit').text('Actualizar');
    $('#servicioId').val(id);

    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);

    // Get service data
    $.ajax({
        url: 'listado-servicios',
        method: 'POST',
        data: {
            action: 'obtener',
            id: id
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#descripcion').val(response.data.descripcion);
                $('#id_servicio_categoria').val(response.data.id_servicio_categoria);
                $('#prioridad').val(response.data.prioridad);
                $('#modalServicio').modal('show');
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error al cargar los datos del servicio.');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
}

// Delete service with confirmation
function eliminarServicio(id) {
    swal({
        title: '¿Está seguro?',
        text: 'Esta acción desactivará el servicio. No podrá revertir esta acción.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: 'Sí, eliminar',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            // Perform delete operation
            $.ajax({
                url: 'listado-servicios',
                method: 'POST',
                data: {
                    action: 'eliminar',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSweetAlertSuccess('Éxito', response.message);
                        setTimeout(() => {
                            location.reload(); // Refresh to update table
                        }, 1500);
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function() {
                    showSweetAlertError('Error', 'Error al eliminar el servicio.');
                }
            });
        }
    });
}

// Client-side form validation
function validateForm() {
    let isValid = true;

    // Clear previous validation states
    $('#formServicio').removeClass('was-validated');
    $('.invalid-feedback').text('');

    // Validate category selection
    const categoria = $('#id_servicio_categoria').val();
    if (!categoria) {
        $('#id_servicio_categoria').addClass('is-invalid');
        $('#id_servicio_categoria').siblings('.invalid-feedback').text('Debe seleccionar una categoría de servicio.');
        isValid = false;
    } else {
        $('#id_servicio_categoria').removeClass('is-invalid').addClass('is-valid');
    }

    // Validate description
    const descripcion = $('#descripcion').val().trim();
    if (!descripcion) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción es requerida.');
        isValid = false;
    } else if (descripcion.length < 3) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción debe tener al menos 3 caracteres.');
        isValid = false;
    } else if (descripcion.length > 255) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción no puede exceder 255 caracteres.');
        isValid = false;
    } else {
        $('#descripcion').removeClass('is-invalid').addClass('is-valid');
    }

    // Validate priority
    const prioridad = $('#prioridad').val();
    if (!prioridad || parseInt(prioridad) <= 0) {
        $('#prioridad').addClass('is-invalid');
        $('#prioridad').siblings('.invalid-feedback').text('La prioridad debe ser un número positivo.');
        isValid = false;
    } else {
        $('#prioridad').removeClass('is-invalid').addClass('is-valid');
    }

    return isValid;
}

// Handle form submission
$('#formServicio').on('submit', function(e) {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
        return;
    }

    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);

    // Submit form via AJAX
    $.ajax({
        url: 'listado-servicios',
        method: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#modalServicio').modal('hide');
                showSweetAlertSuccess('Éxito', response.message);
                setTimeout(() => {
                    location.reload(); // Refresh to update table
                }, 1500);
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error al procesar la solicitud.');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
});

// Real-time validation on input change
$('#id_servicio_categoria').on('change', function() {
    if ($(this).val()) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#descripcion').on('input', function() {
    const value = $(this).val().trim();
    if (value && value.length >= 3 && value.length <= 255) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#prioridad').on('input', function() {
    const value = parseInt($(this).val());
    if (value && value > 0) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

// SweetAlert functions are available from core_js.view.php
