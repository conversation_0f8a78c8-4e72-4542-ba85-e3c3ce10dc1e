/**
 * JavaScript for User Management (lusuarios.js)
 * Handles CRUD operations for users with modals and validation
 */

// Global variables
let usuariosTable;

// Initialize DataTable when document is ready
$(document).ready(function() {
    // Initialize DataTable
    usuariosTable = $('#usuariosTable').DataTable({
        responsive: true,
        language: {
            "sProcessing": "Procesando...",
            "sLengthMenu": "Mostrar _MENU_ registros",
            "sZeroRecords": "No se encontraron resultados",
            "sEmptyTable": "Ningún dato disponible en esta tabla",
            "sInfo": "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
            "sInfoEmpty": "Mostrando registros del 0 al 0 de un total de 0 registros",
            "sInfoFiltered": "(filtrado de un total de _MAX_ registros)",
            "sSearch": "Buscar:",
            "oPaginate": {
                "sFirst": "Primero",
                "sLast": "Último",
                "sNext": "Siguiente",
                "sPrevious": "Anterior"
            }
        },
        order: [[1, 'asc']], // Order by username
        columnDefs: [
            { orderable: false, targets: 0 } // Disable ordering on Actions column
        ]
    });
});

// Open create modal
function abrirModalCrear() {
    $('#modalUsuarioLabel').text('Crear Nuevo Usuario');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#usuarioId').val('');
    $('#formUsuario')[0].reset();
    $('#formUsuario').removeClass('was-validated');
    $('.invalid-feedback').text('');
    
    // Show all fields for create
    $('#usernameGroup').show();
    $('#passwordGroup').show();
    $('#confirmPasswordGroup').show();
    
    // Make fields required for create
    $('#username').prop('required', true);
    $('#password').prop('required', true);
    $('#confirm_password').prop('required', true);
}

// Open edit modal
function abrirModalEditar(id) {
    $('#modalUsuarioLabel').text('Editar Usuario');
    $('#action').val('editar');
    $('#btnSubmit').text('Actualizar');
    $('#usuarioId').val(id);
    $('#formUsuario')[0].reset();
    $('#formUsuario').removeClass('was-validated');
    $('.invalid-feedback').text('');

    // Hide username and password fields for edit
    $('#usernameGroup').hide();
    $('#passwordGroup').hide();
    $('#confirmPasswordGroup').hide();

    // Make fields not required for edit
    $('#username').prop('required', false);
    $('#password').prop('required', false);
    $('#confirm_password').prop('required', false);

    // Show the modal
    $('#modalUsuario').modal('show');

    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);

    // Fetch user data
    $.ajax({
        url: window.location.href,
        method: 'POST',
        data: {
            action: 'obtener',
            id: id
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#nombre').val(response.data.nombre);
            } else {
                showSweetAlertError('Error', response.message);
                $('#modalUsuario').modal('hide');
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error de conexión al obtener los datos del usuario.');
            $('#modalUsuario').modal('hide');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
}

// Open change password modal
function abrirModalCambiarClave(id) {
    $('#claveUsuarioId').val(id);
    $('#formCambiarClave')[0].reset();
    $('#formCambiarClave').removeClass('was-validated');
    $('.invalid-feedback').text('');
    $('#modalCambiarClave').modal('show');
}

// Delete user with confirmation
function eliminarUsuario(id) {
    swal({
        title: '¿Está seguro?',
        text: 'Esta acción desactivará el usuario. No podrá revertir esta acción.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true
            },
            confirm: {
                text: 'Sí, desactivar',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: false
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            // Show loading in the confirm button
            $('.swal-button--confirm').html('<span class="spinner-border spinner-border-sm me-1"></span>Desactivando...');
            $('.swal-button--confirm').prop('disabled', true);
            
            $.ajax({
                url: window.location.href,
                method: 'POST',
                data: {
                    action: 'eliminar',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    swal.close();
                    if (response.success) {
                        showSweetAlertSuccess('¡Éxito!', response.message);
                        // Reload the page to refresh the table
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function() {
                    swal.close();
                    showSweetAlertError('Error', 'Error de conexión al desactivar el usuario.');
                }
            });
        }
    });
}

// Handle user form submission
$('#formUsuario').on('submit', function(e) {
    e.preventDefault();
    
    // Client-side validation
    if (!this.checkValidity()) {
        e.stopPropagation();
        $(this).addClass('was-validated');
        return;
    }
    
    // Additional validation for passwords
    const action = $('#action').val();
    if (action === 'crear') {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        
        if (password !== confirmPassword) {
            $('#confirm_password').addClass('is-invalid');
            $('#confirm_password').siblings('.invalid-feedback').text('Las contraseñas no coinciden.');
            return;
        }
    }
    
    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);
    
    // Submit form
    $.ajax({
        url: window.location.href,
        method: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#modalUsuario').modal('hide');
                showSweetAlertSuccess('¡Éxito!', response.message);
                // Reload the page to refresh the table
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error de conexión al procesar la solicitud.');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
});

// Handle change password form submission
$('#formCambiarClave').on('submit', function(e) {
    e.preventDefault();
    
    // Client-side validation
    if (!this.checkValidity()) {
        e.stopPropagation();
        $(this).addClass('was-validated');
        return;
    }
    
    // Additional validation for passwords
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_new_password').val();
    
    if (newPassword !== confirmPassword) {
        $('#confirm_new_password').addClass('is-invalid');
        $('#confirm_new_password').siblings('.invalid-feedback').text('Las contraseñas no coinciden.');
        return;
    }
    
    // Show loading state
    $('#loadingSpinnerClave').removeClass('d-none');
    $('#btnSubmitClave').prop('disabled', true);
    
    // Submit form
    $.ajax({
        url: window.location.href,
        method: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#modalCambiarClave').modal('hide');
                showSweetAlertSuccess('¡Éxito!', response.message);
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error de conexión al cambiar la contraseña.');
        },
        complete: function() {
            $('#loadingSpinnerClave').addClass('d-none');
            $('#btnSubmitClave').prop('disabled', false);
        }
    });
});

// Real-time validation on input change
$('#username').on('input', function() {
    const value = $(this).val().trim();
    if (value && value.length >= 3) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#password').on('input', function() {
    const value = $(this).val();
    if (value && value.length >= 5) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#confirm_password').on('input', function() {
    const password = $('#password').val();
    const confirmPassword = $(this).val();
    if (confirmPassword && confirmPassword === password) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#new_password').on('input', function() {
    const value = $(this).val();
    if (value && value.length >= 5) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#confirm_new_password').on('input', function() {
    const newPassword = $('#new_password').val();
    const confirmPassword = $(this).val();
    if (confirmPassword && confirmPassword === newPassword) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

$('#nombre').on('input', function() {
    const value = $(this).val().trim();
    if (value && value.length >= 2) {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});

// Auto-capitalize username input
$('#username').on('input', function() {
    this.value = this.value.toUpperCase();
});

// SweetAlert functions are available from core_js.view.php
