document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const categoryTabs = document.querySelectorAll('.category-tab');
    const categoryContents = document.querySelectorAll('.category-content');
    
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs
            categoryTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            tab.classList.add('active');
            
            // Hide all content sections
            categoryContents.forEach(content => content.classList.remove('active'));
            
            // Show the corresponding content section
            const category = tab.getAttribute('data-category');
            document.getElementById(`${category}-content`).classList.add('active');
        });
    });
});
