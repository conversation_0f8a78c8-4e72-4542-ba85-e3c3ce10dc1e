/**
 * Dashboard Configuration Form JavaScript
 * Handles client-side validation and user interaction
 */

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('config-form');
    const correoInput = document.getElementById('correo');
    const telefonoInput = document.getElementById('telefono');
    const facebookInput = document.getElementById('link_facebook');
    const instagramInput = document.getElementById('link_instagram');
    const linkedinInput = document.getElementById('link_linkedin');

    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // URL validation regex
    const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

    /**
     * Validate email field
     */
    function validateEmail(input) {
        const value = input.value.trim();

        // Email is optional, so empty is valid
        if (value === '') {
            setFieldValid(input);
            return true;
        }

        // Check email format
        if (emailRegex.test(value)) {
            setFieldValid(input);
            return true;
        } else {
            setFieldInvalid(input, 'Por favor, ingrese un correo electrónico válido.');
            return false;
        }
    }

    /**
     * Validate URL fields
     */
    function validateUrl(input) {
        const value = input.value.trim();

        // URL is optional, so empty is valid
        if (value === '') {
            setFieldValid(input);
            return true;
        }

        // Check URL format
        if (urlRegex.test(value)) {
            setFieldValid(input);
            return true;
        } else {
            setFieldInvalid(input, 'Por favor, ingrese una URL válida (debe comenzar con http:// o https://).');
            return false;
        }
    }

    /**
     * Validate phone field (basic validation)
     */
    function validatePhone(input) {
        const value = input.value.trim();

        // Phone is optional, so empty is valid
        if (value === '') {
            setFieldValid(input);
            return true;
        }

        // Basic phone validation (allow numbers, spaces, +, -, (, ))
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,20}$/;

        if (phoneRegex.test(value)) {
            setFieldValid(input);
            return true;
        } else {
            setFieldInvalid(input, 'Por favor, ingrese un número de teléfono válido.');
            return false;
        }
    }

    /**
     * Set field as valid
     */
    function setFieldValid(input) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'none';
        }
    }

    /**
     * Set field as invalid
     */
    function setFieldInvalid(input, message) {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
            feedback.style.display = 'block';
        }
    }

    /**
     * Clear field validation state
     */
    function clearFieldValidation(input) {
        input.classList.remove('is-valid', 'is-invalid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'none';
        }
    }

    /**
     * Initialize form - clear all validation states on page load
     */
    function initializeForm() {
        const allInputs = [correoInput, telefonoInput, facebookInput, instagramInput, linkedinInput];

        allInputs.forEach(input => {
            if (input) {
                clearFieldValidation(input);
            }
        });
    }

    // Real-time validation on input/blur events
    if (correoInput) {
        correoInput.addEventListener('blur', function() {
            validateEmail(this);
        });

        correoInput.addEventListener('input', function() {
            // Clear validation state while typing
            if (this.classList.contains('is-invalid')) {
                clearFieldValidation(this);
            }
        });
    }

    if (telefonoInput) {
        telefonoInput.addEventListener('blur', function() {
            validatePhone(this);
        });

        telefonoInput.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                clearFieldValidation(this);
            }
        });
    }

    // URL field validations
    [facebookInput, instagramInput, linkedinInput].forEach(input => {
        if (input) {
            input.addEventListener('blur', function() {
                validateUrl(this);
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    clearFieldValidation(this);
                }
            });
        }
    });

    // Form submission validation
    if (form) {
        form.addEventListener('submit', function(event) {
            let isValid = true;

            // Validate all fields
            if (correoInput && !validateEmail(correoInput)) {
                isValid = false;
            }

            if (telefonoInput && !validatePhone(telefonoInput)) {
                isValid = false;
            }

            if (facebookInput && !validateUrl(facebookInput)) {
                isValid = false;
            }

            if (instagramInput && !validateUrl(instagramInput)) {
                isValid = false;
            }

            if (linkedinInput && !validateUrl(linkedinInput)) {
                isValid = false;
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Focus the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }

                // Show error message
                if (typeof showSweetAlertError === 'function') {
                    showSweetAlertError('Error de Validación', 'Por favor, corrija los errores en el formulario antes de continuar.');
                }
            } else {
                // Add loading state
                form.classList.add('loading');
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Guardando...';

                    // Restore button state if form submission fails (fallback)
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                        form.classList.remove('loading');
                    }, 10000); // 10 seconds timeout
                }
            }
        });
    }

    // Auto-format URLs (add https:// if missing)
    [facebookInput, instagramInput, linkedinInput].forEach(input => {
        if (input) {
            input.addEventListener('blur', function() {
                const value = this.value.trim();
                if (value && !value.startsWith('http://') && !value.startsWith('https://')) {
                    this.value = 'https://' + value;
                    validateUrl(this);
                }
            });
        }
    });

    // Initialize form - clear any validation states on page load
    initializeForm();

    console.log('Dashboard configuration form initialized successfully.');
});