
// Global variables
let dataTable;

// Initialize DataTable
$(document).ready(function() {
    dataTable = $('#data-table-categorias').DataTable({
        responsive: true,
        language: {
            "sProcessing": "Procesando...",
            "sLengthMenu": "Mostrar _MENU_ registros",
            "sZeroRecords": "No se encontraron resultados",
            "sEmptyTable": "Ningún dato disponible en esta tabla",
            "sInfo": "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
            "sInfoEmpty": "Mostrando registros del 0 al 0 de un total de 0 registros",
            "sInfoFiltered": "(filtrado de un total de _MAX_ registros)",
            "sSearch": "Buscar:",
            "oPaginate": {
                "sFirst": "Primero",
                "sLast": "Último",
                "sNext": "Siguiente",
                "sPrevious": "Anterior"
            }
        },
        order: [[3, 'asc'], [1, 'asc']], // Order by priority, then description
        columnDefs: [
            { orderable: false, targets: 0 } // Disable ordering on Actions column
        ]
    });
});

// Open create modal
function abrirModalCrear() {
    $('#modalCategoriaLabel').text('Crear Nueva Categoría');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#categoriaId').val('');
    $('#formCategoria')[0].reset();
    $('#formCategoria').removeClass('was-validated');
    $('.invalid-feedback').text('');
}

// Open edit modal
function abrirModalEditar(id) {
    $('#modalCategoriaLabel').text('Editar Categoría');
    $('#action').val('editar');
    $('#btnSubmit').text('Actualizar');
    $('#categoriaId').val(id);
    
    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);
    
    // Get category data
    $.ajax({
        url: 'listado-servicios-categorias',
        method: 'POST',
        data: {
            action: 'obtener',
            id: id
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#descripcion').val(response.data.descripcion);
                $('#icono').val(response.data.icono);
                $('#prioridad').val(response.data.prioridad);
                $('#modalCategoria').modal('show');
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error al cargar los datos de la categoría.');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
}

// Delete category with confirmation
function eliminarCategoria(id) {
    swal({
        title: '¿Está seguro?',
        text: 'Esta acción desactivará la categoría. No podrá revertir esta acción.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: 'Sí, eliminar',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: 'listado-servicios-categorias',
                method: 'POST',
                data: {
                    action: 'eliminar',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSweetAlertSuccess('Éxito', response.message);
                        location.reload(); // Refresh to update table
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function() {
                    showSweetAlertError('Error', 'Error al eliminar la categoría.');
                }
            });
        }
    });
}

// Form validation and submission
$('#formCategoria').on('submit', function(e) {
    e.preventDefault();
    
    // Reset validation state
    $(this).removeClass('was-validated');
    $('.invalid-feedback').text('');
    
    // Client-side validation
    let isValid = true;
    
    // Validate descripcion
    const descripcion = $('#descripcion').val().trim();
    if (!descripcion) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción es requerida.');
        isValid = false;
    } else if (descripcion.length < 3) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción debe tener al menos 3 caracteres.');
        isValid = false;
    } else if (descripcion.length > 100) {
        $('#descripcion').addClass('is-invalid');
        $('#descripcion').siblings('.invalid-feedback').text('La descripción no puede exceder 100 caracteres.');
        isValid = false;
    } else {
        $('#descripcion').removeClass('is-invalid').addClass('is-valid');
    }
    
    // Validate icono
    const icono = $('#icono').val().trim();
    if (!icono) {
        $('#icono').addClass('is-invalid');
        $('#icono').siblings('.invalid-feedback').text('El icono es requerido.');
        isValid = false;
    } else {
        $('#icono').removeClass('is-invalid').addClass('is-valid');
    }
    
    // Validate prioridad
    const prioridad = parseInt($('#prioridad').val());
    if (!prioridad || prioridad <= 0) {
        $('#prioridad').addClass('is-invalid');
        $('#prioridad').siblings('.invalid-feedback').text('La prioridad debe ser un número positivo.');
        isValid = false;
    } else {
        $('#prioridad').removeClass('is-invalid').addClass('is-valid');
    }
    
    if (!isValid) {
        return;
    }
    
    // Show loading state
    $('#loadingSpinner').removeClass('d-none');
    $('#btnSubmit').prop('disabled', true);
    
    // Submit form via AJAX
    $.ajax({
        url: 'listado-servicios-categorias',
        method: 'POST',
        data: $(this).serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#modalCategoria').modal('hide');
                showSweetAlertSuccess('Éxito', response.message);
                setTimeout(() => {
                    location.reload(); // Refresh to update table
                }, 1500);
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function() {
            showSweetAlertError('Error', 'Error al procesar la solicitud.');
        },
        complete: function() {
            $('#loadingSpinner').addClass('d-none');
            $('#btnSubmit').prop('disabled', false);
        }
    });
});

// Reset validation on modal close
$('#modalCategoria').on('hidden.bs.modal', function() {
    $('#formCategoria')[0].reset();
    $('#formCategoria').removeClass('was-validated');
    $('.form-control').removeClass('is-valid is-invalid');
    $('.invalid-feedback').text('');
});
