document.addEventListener('DOMContentLoaded', function () {
    const form              = document.getElementById('change-password-form');
    const nuevaClaveInput   = document.getElementById('nueva_clave');
    const confirmClaveInput = document.getElementById('confirm_clave');
    
    // Error message elements
    const nuevaClaveError   = document.getElementById('nueva_clave-error');
    const confirmClaveError = document.getElementById('confirm_clave-error');
    
    // Hide server-rendered messages if user starts typing
    [nuevaClaveInput, confirmClaveInput].forEach(input => {
        input.addEventListener('input', () => {
            document.getElementById('success-message').classList.remove('show');
            document.getElementById('success-message').classList.add('hide');
            document.getElementById('error-message').classList.remove('show');
            document.getElementById('error-message').classList.add('hide');
        });
    });
    
    form.addEventListener('submit', function (event) {
        let isValid = true;
        
        // Reset previous validation states
        [nuevaClaveInput, confirmClaveInput].forEach(input => {
            input.classList.remove('is-invalid');
        });
        // Reset error messages to defaults (or clear them)
        nuevaClaveError.textContent   = 'La nueva contraseña es requerida.';
        confirmClaveError.textContent = 'Por favor confirme la nueva contraseña.';
        
        // Validate Nueva Clave (Password)
        if (nuevaClaveInput.value === '') {
            isValid = false;
            nuevaClaveInput.classList.add('is-invalid');
            // Keep default message nuevaClaveError.textContent = '...';
        }
        // Optional: Add minimum length check
        else if (nuevaClaveInput.value.length < 6) { // Example: minimum 6 characters
            isValid = false;
            nuevaClaveInput.classList.add('is-invalid');
            nuevaClaveError.textContent = 'La contraseña debe tener al menos 6 caracteres.';
        }
        
        
        // Validate Confirm Clave (Confirm Password)
        if (confirmClaveInput.value === '') {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            // Keep default message confirmClaveError.textContent = '...';
        } else if (nuevaClaveInput.value !== confirmClaveInput.value) {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            confirmClaveError.textContent = 'Las contraseñas no coinciden.';
        }
        
        // Prevent form submission if validation fails
        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
            // Optional: focus the first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
            }
        }
        // Otherwise, allow the form to submit to the server (cambiar_clave.php)
    });
    
    // Optional: Real-time feedback as user types (or on blur)
    [nuevaClaveInput, confirmClaveInput].forEach(input => {
        input.addEventListener('input', () => {
            // Simple check to remove invalid state when user starts typing
            if (input.classList.contains('is-invalid')) {
                input.classList.remove('is-invalid');
            }
            // More complex: re-validate on blur or input for better UX
        });
    });
    
});