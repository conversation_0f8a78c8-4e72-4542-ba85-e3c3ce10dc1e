<?php

$host = 'localhost';
$db   = '';
$user = '';
$pass = '';

if(IS_PRODUCTIVO == 0){
	$db   = 'serimpro';
	$user = 'root';
} else{
	$db   = 'u241348086_serimprodb';
	$user = 'u241348086_serimprou';
	$pass = 'Imserop.xkw129!';
}

$port = "3306";
$charset = 'utf8mb4';

$options = [
    \PDO::ATTR_ERRMODE            => \PDO::ERRMODE_EXCEPTION,
    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
    \PDO::ATTR_EMULATE_PREPARES   => false,
];

$dsn = "mysql:host=$host;dbname=$db;charset=$charset;port=$port";

try {
     $conexion = new \PDO($dsn, $user, $pass, $options);

} catch (\PDOException $e) {
     throw new \PDOException($e->getMessage(), (int)$e->getCode());
}

?>