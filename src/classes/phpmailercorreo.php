<?php

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception as PHPMailerException;

// Include PHPMailer files
require_once __ROOT__ . '/vendor/autoload.php';

class PHPMailerCorreo
{
	/**
	 * @throws Exception // Use fully qualified name for standard Exception
	 */
	private static function enviar_correo($paramref): void
	{
		$mail = new PHPMailer(true); // Initialize mail object outside try block for error info access
		try {
			$micorreo     = '<EMAIL>';
			$destinatario = $paramref['destinatario'];
			$subject      = $paramref['subject'];
			$body         = $paramref['body'];

			$mail = new PHPMailer(true);

			// SMTP configuration
			$mail->isSMTP();
			$mail->Host       = 'smtp.hostinger.com';    // SMTP server
			$mail->SMTPAuth   = true;
			$mail->Username   = $micorreo;
			$mail->Password   = 'Dosep.no210*';
			$mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
			$mail->Port       = 465;                     // TCP port

			// Configuración de codificación
			$mail->CharSet = 'UTF-8';

			// Email settings
			$mail->setFrom($micorreo, 'No responder ' . NOMBRE_EMPRESA_ABREV);
			$mail->addAddress($destinatario);
			$mail->Subject = $subject;
			$mail->isHTML(true);
			$mail->Body = $body;

			// Send email
			$mail->send();

		} catch (PHPMailerException $e) { // Catch specific PHPMailer exception
			// Rethrow as a generic \Exception, including PHPMailer's detailed error message
			throw new Exception("Error al enviar correo: {$mail->ErrorInfo}");
		} catch (Exception $e) { // Catch any other standard exceptions
			// Rethrow as a generic \Exception
			throw new Exception("Error inesperado en el envío de correo: " . $e->getMessage());
		}
	}

	/**
	 * @throws Exception // Use fully qualified name
	 */
	public static function enviar_correo_contactanos($paramref): void
	{
		//parametros
		$nombre   = $paramref['nombre'];
		$correo   = $paramref['correo'];
		$telefono = $paramref['telefono'];
		$asunto   = $paramref['asunto'];
		$mensaje  = $paramref['mensaje'];

		//validaciones
		validar_campovacio($nombre, 'Debe especificar el nombre');
		validar_campovacio($correo, 'Debe especificar el correo');
		if (!filter_var($correo, FILTER_VALIDATE_EMAIL)) {
			throw new Exception('El correo electrónico no tiene un formato válido');
		}
		validar_campovacio($telefono, 'Debe especificar el telefono');
		validar_campovacio($asunto, 'Debe especificar el asunto');
		validar_campovacio($mensaje, 'Debe especificar el mensaje');

		$body = 'Ha recibido un mensaje desde el formulario de contacto de la página web:<br><br>';
		$body .= '<b>Nombre:</b> ' . $nombre . '<br>';
		$body .= '<b>Correo:</b> ' . $correo . '<br>';
		$body .= '<b>Teléfono:</b> ' . $telefono . '<br>';
		$body .= '<b>Asunto:</b> ' . $asunto . '<br>';
		$body .= '<br>';
		$body .= '<b>Mensaje:</b><br> ' . $mensaje . '<br>';
		$body .= '<br>';
		$body .= '-- Esto es un mensaje automático.';

		//enviar correo
		$param                 = array();
		$param['subject']      = NOMBRE_EMPRESA_ABREV . ' - Mensaje desde formulario de contacto';
		//$param['destinatario'] = '<EMAIL>';
		$param['destinatario'] = '<EMAIL>';
		$param['body']         = $body;
		self::enviar_correo($param);
	}
}
