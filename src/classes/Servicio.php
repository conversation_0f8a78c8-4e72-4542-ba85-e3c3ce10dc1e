<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Servicio
{
	// --- Atributos ---
	private ?int    $id                    = null;
	private ?int    $id_servicio_categoria = null;
	private ?string $descripcion           = null;
	private ?int    $prioridad             = null;
	private ?int    $estado                = null;

	// --- Atributos para datos de categoría unidos (JOIN) ---
	private ?int    $joined_categoria_id          = null;
	private ?string $joined_categoria_descripcion = null;
	private ?string $joined_categoria_icono       = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Servicio.
	 */
	public function __construct()
	{
		$this->id                    = 0; // O null si prefieres no usar 0 por defecto
		$this->id_servicio_categoria = null;
		$this->descripcion           = null;
		$this->prioridad             = null;
		$this->estado                = 1; // Estado activo por defecto

		// Inicializar propiedades de categoría unida
		$this->joined_categoria_id          = null;
		$this->joined_categoria_descripcion = null;
		$this->joined_categoria_icono       = null;
	}

	/**
	 * Método estático para construir un objeto Servicio desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del servicio.
	 *
	 * @return self Instancia de Servicio.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                          = new self();
			$objeto->id                      = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_servicio_categoria   = isset($resultado['id_servicio_categoria']) ? (int)$resultado['id_servicio_categoria'] : null;
			$objeto->descripcion             = $resultado['descripcion'] ?? null;
			$objeto->prioridad               = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : null;
			$objeto->estado                  = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;

			// Poblar datos de categoría unidos si están presentes
			$objeto->joined_categoria_id          = isset($resultado['categoria_id']) ? (int)$resultado['categoria_id'] : null;
			$objeto->joined_categoria_descripcion = $resultado['categoria_descripcion'] ?? null;
			$objeto->joined_categoria_icono       = $resultado['categoria_icono'] ?? null;

			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Servicio: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un servicio por su ID.
	 *
	 * @param int $id       ID del servicio.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Servicio o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener servicio por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	s.*
            FROM servicios s
            WHERE
            	s.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'servicios'

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Servicio (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de servicios activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Servicio.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de servicios activos con datos de categoría ordenados por categoría y prioridad (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	s.*,
            	sc.id as categoria_id,
            	sc.descripcion as categoria_descripcion,
            	sc.icono as categoria_icono
            FROM servicios s
            LEFT JOIN servicios_categorias sc ON s.id_servicio_categoria = sc.id
            WHERE
            	s.estado = 1
            ORDER BY
            	sc.descripcion ASC,
            	s.prioridad ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Servicios: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de servicios activos por categoría.
	 *
	 * @param int $id_categoria ID de la categoría.
	 * @param PDO $conexion     Conexión PDO.
	 *
	 * @return array Array de objetos Servicio.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_by_categoria(int $id_categoria, PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de servicios activos por categoría (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	s.*,
            	sc.id as categoria_id,
            	sc.descripcion as categoria_descripcion,
            	sc.icono as categoria_icono
            FROM servicios s
            LEFT JOIN servicios_categorias sc ON s.id_servicio_categoria = sc.id
            WHERE
            	s.estado = 1
            	AND s.id_servicio_categoria = :id_categoria
            ORDER BY
            	sc.descripcion ASC,
            	s.prioridad ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_categoria', $id_categoria, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Servicios por categoría (ID: $id_categoria): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo servicio en la base de datos a partir de un objeto Servicio.
	 * El objeto Servicio debe estar completamente poblado con al menos la descripción y categoría.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo servicio creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || $this->getId_servicio_categoria() === null) {
			throw new Exception("La descripción e id_servicio_categoria son requeridos en el objeto Servicio para crearlo.");
		}

		try {
			$descripcion = $this->getDescripcion(); // Para usar en mensaje de error

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO servicios (
            	 id_servicio_categoria
            	,descripcion
            	,prioridad
            	,estado
            ) VALUES (
            	 :id_servicio_categoria
            	,:descripcion
            	,:prioridad
            	,:estado
            )
            SQL;
			// Asume tabla 'servicios'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_servicio_categoria', $this->getId_servicio_categoria(), PDO::PARAM_INT);
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del servicio recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al crear servicio: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear servicio: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un servicio existente.
	 *
	 * @param int      $id                    ID del servicio a modificar.
	 * @param string   $descripcion           Nueva descripción para el servicio.
	 * @param int      $id_servicio_categoria ID de la categoría del servicio.
	 * @param int|null $prioridad             Nueva prioridad para el servicio (opcional).
	 * @param PDO      $conexion              Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $descripcion, int $id_servicio_categoria, ?int $prioridad, PDO $conexion): bool
	{
		if (empty(trim($descripcion))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($id_servicio_categoria <= 0) {
			throw new Exception("El ID de la categoría debe ser un valor válido mayor que cero.");
		}

		try {
			// Consulta para actualizar la descripción, categoría y prioridad
			$query = <<<SQL
            UPDATE servicios SET
                descripcion = :descripcion,
                id_servicio_categoria = :id_servicio_categoria,
                prioridad = :prioridad
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($descripcion), PDO::PARAM_STR);
			$statement->bindValue(':id_servicio_categoria', $id_servicio_categoria, PDO::PARAM_INT);
			$statement->bindValue(':prioridad', $prioridad, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada,
			// aunque si los datos son los mismos, rowCount será 0 pero la operación es "exitosa".
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al modificar servicio (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un servicio estableciendo su estado a 0.
	 *
	 * @param int $id       ID del servicio a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE servicios SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar servicio (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene la categoría asociada al servicio.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return ServicioCategoria|null Objeto ServicioCategoria o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public function getServicioCategoria(PDO $conexion): ?ServicioCategoria
	{
		if ($this->id_servicio_categoria === null) {
			return null;
		}

		return ServicioCategoria::get($this->id_servicio_categoria, $conexion);
	}

	/**
	 * Obtiene la categoría desde los datos unidos (JOIN) sin hacer consulta adicional.
	 * Este método debe usarse cuando los datos se obtuvieron con get_list() que incluye JOIN.
	 *
	 * @return ServicioCategoria|null Objeto ServicioCategoria construido desde datos unidos o null si no hay datos.
	 */
	public function getCategoriaFromJoin(): ?ServicioCategoria
	{
		if (!$this->hasCategoriaJoined()) {
			return null;
		}

		// Construir objeto ServicioCategoria desde los datos unidos
		$categoriaData = [
			'id' => $this->joined_categoria_id,
			'descripcion' => $this->joined_categoria_descripcion,
			'icono' => $this->joined_categoria_icono,
			'estado' => 1 // Asumimos que está activa si está en el JOIN
		];

		return ServicioCategoria::construct($categoriaData);
	}

	/**
	 * Verifica si el objeto tiene datos de categoría unidos (cargados via JOIN).
	 *
	 * @return bool True si tiene datos de categoría unidos, false en caso contrario.
	 */
	public function hasCategoriaJoined(): bool
	{
		return $this->joined_categoria_id !== null;
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getId_servicio_categoria(): ?int
	{
		return $this->id_servicio_categoria;
	}

	public function setId_servicio_categoria(?int $id_servicio_categoria): self
	{
		$this->id_servicio_categoria = $id_servicio_categoria;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Getters para datos de categoría unidos ---

	public function getJoinedCategoriaId(): ?int
	{
		return $this->joined_categoria_id;
	}

	public function getJoinedCategoriaDescripcion(): ?string
	{
		return $this->joined_categoria_descripcion;
	}

	public function getJoinedCategoriaIcono(): ?string
	{
		return $this->joined_categoria_icono;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si el servicio está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

}
