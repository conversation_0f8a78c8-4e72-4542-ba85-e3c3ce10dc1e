<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

/**
 * Clase ConfigWeb para manejar la configuración web del sistema.
 * Maneja información de contacto y enlaces de redes sociales.
 */
class ConfigWeb
{
    // --- Atributos ---
    private ?int    $id             = null;
    private ?string $telefono       = null;
    private ?string $correo         = null;
    private ?string $link_facebook  = null;
    private ?string $link_instagram = null;
    private ?string $link_linkedin  = null;

    /**
     * Constructor: Inicializa las propiedades del objeto ConfigWeb.
     */
    public function __construct()
    {
        $this->id             = 0;
        $this->telefono       = null;
        $this->correo         = null;
        $this->link_facebook  = null;
        $this->link_instagram = null;
        $this->link_linkedin  = null;
    }

    /**
     * Método estático para construir un objeto ConfigWeb desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de configuración web.
     *
     * @return self Instancia de ConfigWeb.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                  = new self();
            $objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->telefono        = $resultado['telefono'] ?? null;
            $objeto->correo          = $resultado['correo'] ?? null;
            $objeto->link_facebook   = $resultado['link_facebook'] ?? null;
            $objeto->link_instagram  = $resultado['link_instagram'] ?? null;
            $objeto->link_linkedin   = $resultado['link_linkedin'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir ConfigWeb: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene una configuración web por su ID.
     *
     * @param int $id       ID de la configuración.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto ConfigWeb o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                cw.*
            FROM configs_web cw
            WHERE
                cw.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener ConfigWeb (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene la primera configuración web disponible (configuración principal).
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto ConfigWeb o null si no existe configuración.
     * @throws Exception Si hay error en DB.
     */
    public static function getPrincipal(PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                cw.*
            FROM configs_web cw
            ORDER BY
                cw.id ASC
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener configuración principal: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todas las configuraciones web.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos ConfigWeb.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                cw.*
            FROM configs_web cw
            ORDER BY
                cw.id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de ConfigWeb: " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva configuración web en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva configuración creada o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    public function crear(PDO $conexion): int|false
    {
        try {
            $query = <<<SQL
            INSERT INTO configs_web (
                 telefono
                ,correo
                ,link_facebook
                ,link_instagram
                ,link_linkedin
            ) VALUES (
                 :telefono
                ,:correo
                ,:link_facebook
                ,:link_instagram
                ,:link_linkedin
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':telefono', $this->getTelefono(), PDO::PARAM_STR);
            $statement->bindValue(':correo', $this->getCorreo(), PDO::PARAM_STR);
            $statement->bindValue(':link_facebook', $this->getLinkFacebook(), PDO::PARAM_STR);
            $statement->bindValue(':link_instagram', $this->getLinkInstagram(), PDO::PARAM_STR);
            $statement->bindValue(':link_linkedin', $this->getLinkLinkedin(), PDO::PARAM_STR);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear configuración web: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear configuración web: " . $e->getMessage());
        }
    }

    /**
     * Modifica una configuración web existente.
     *
     * @param int    $id       ID de la configuración a modificar.
     * @param string $telefono Nuevo teléfono.
     * @param string $correo   Nuevo correo.
     * @param string $link_facebook Nuevo enlace de Facebook.
     * @param string $link_instagram Nuevo enlace de Instagram.
     * @param string $link_linkedin Nuevo enlace de LinkedIn.
     * @param PDO    $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function modificar(
        int $id,
        ?string $telefono,
        ?string $correo,
        ?string $link_facebook,
        ?string $link_instagram,
        ?string $link_linkedin,
        PDO $conexion
    ): bool {
        try {
            $query = <<<SQL
            UPDATE configs_web SET
                telefono = :telefono,
                correo = :correo,
                link_facebook = :link_facebook,
                link_instagram = :link_instagram,
                link_linkedin = :link_linkedin
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':telefono', $telefono, PDO::PARAM_STR);
            $statement->bindValue(':correo', $correo, PDO::PARAM_STR);
            $statement->bindValue(':link_facebook', $link_facebook, PDO::PARAM_STR);
            $statement->bindValue(':link_instagram', $link_instagram, PDO::PARAM_STR);
            $statement->bindValue(':link_linkedin', $link_linkedin, PDO::PARAM_STR);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar configuración web (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Elimina una configuración web.
     *
     * @param int $id       ID de la configuración a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM configs_web
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar configuración web (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getTelefono(): ?string
    {
        return $this->telefono;
    }

    public function setTelefono(?string $telefono): self
    {
        $this->telefono = $telefono;
        return $this;
    }

    public function getCorreo(): ?string
    {
        return $this->correo;
    }

    public function setCorreo(?string $correo): self
    {
        $this->correo = $correo;
        return $this;
    }

    public function getLinkFacebook(): ?string
    {
        return $this->link_facebook;
    }

    public function setLinkFacebook(?string $link_facebook): self
    {
        $this->link_facebook = $link_facebook;
        return $this;
    }

    public function getLinkInstagram(): ?string
    {
        return $this->link_instagram;
    }

    public function setLinkInstagram(?string $link_instagram): self
    {
        $this->link_instagram = $link_instagram;
        return $this;
    }

    public function getLinkLinkedin(): ?string
    {
        return $this->link_linkedin;
    }

    public function setLinkLinkedin(?string $link_linkedin): self
    {
        $this->link_linkedin = $link_linkedin;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si tiene información de contacto configurada.
     * @return bool
     */
    public function tieneContacto(): bool
    {
        return !empty($this->telefono) || !empty($this->correo);
    }

    /**
     * Verifica si tiene enlaces de redes sociales configurados.
     * @return bool
     */
    public function tieneRedesSociales(): bool
    {
        return !empty($this->link_facebook) || !empty($this->link_instagram) || !empty($this->link_linkedin);
    }

    /**
     * Obtiene un array con todos los enlaces de redes sociales no vacíos.
     * @return array
     */
    public function getRedesSociales(): array
    {
        $redes = [];
        
        if (!empty($this->link_facebook)) {
            $redes['facebook'] = $this->link_facebook;
        }
        
        if (!empty($this->link_instagram)) {
            $redes['instagram'] = $this->link_instagram;
        }
        
        if (!empty($this->link_linkedin)) {
            $redes['linkedin'] = $this->link_linkedin;
        }
        
        return $redes;
    }
}
