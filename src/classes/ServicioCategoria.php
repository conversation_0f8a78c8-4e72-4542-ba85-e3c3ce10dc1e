<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class ServicioCategoria
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $descripcion = null;
	private ?int    $prioridad   = null;
	private ?string $icono       = null;
	private ?int    $estado      = null;
	private array   $servicios   = [];
	
	/**
	 * Constructor: Inicializa las propiedades del objeto ServicioCategoria.
	 */
	public function __construct()
	{
		$this->id          = 0; // O null si prefieres no usar 0 por defecto
		$this->descripcion = null;
		$this->prioridad   = null;
		$this->icono       = null;
		$this->estado      = 1; // Estado activo por defecto
		$this->servicios   = []; // Array vacío por defecto
	}
	
	/**
	 * Método estático para construir un objeto ServicioCategoria desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la categoría.
	 *
	 * @return self Instancia de ServicioCategoria.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                = new self();
			$objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion   = $resultado['descripcion'] ?? null;
			$objeto->prioridad     = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : null;
			$objeto->icono         = $resultado['icono'] ?? null;
			$objeto->estado        = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->servicios   = []; // Inicializar array vacío de servicios
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir ServicioCategoria: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene una categoría por su ID.
	 *
	 * @param int $id       ID de la categoría.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto ServicioCategoria o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener categoría por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	sc.*
            FROM servicios_categorias sc
            WHERE
            	sc.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'servicios_categorias'
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener ServicioCategoria (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de categorías activas.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos ServicioCategoria.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de categorías activas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	sc.*
            FROM servicios_categorias sc
            WHERE
            	sc.estado = 1
            ORDER BY
            	sc.prioridad ASC,
            	sc.descripcion ASC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de ServicioCategoria: " . $e->getMessage());
		}
	}
	
	/**
	 * Crea una nueva categoría en la base de datos a partir de un objeto ServicioCategoria.
	 * El objeto ServicioCategoria debe estar completamente poblado con al menos la descripción.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva categoría creada o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción es requerida en el objeto ServicioCategoria para crearlo.");
		}
		
		try {
			$descripcion = $this->getDescripcion(); // Para usar en mensaje de error
			
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO servicios_categorias (
            	 descripcion
            	,prioridad
            	,icono
            	,estado
            ) VALUES (
            	 :descripcion
            	,:prioridad
            	,:icono
            	,:estado
            )
            SQL;
			// Asume tabla 'servicios_categorias'
			
			$statement = $conexion->prepare($query);
			
			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':icono', $this->getIcono(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);
			
			// Ejecutar la consulta
			$success = $statement->execute();
			
			if ($success) {
				// Devolver el ID de la categoría recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}
			
		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al crear categoría: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear categoría: " . $e->getMessage());
		}
	}
	
	/**
	 * Modifica una categoría existente.
	 *
	 * @param int         $id          ID de la categoría a modificar.
	 * @param string      $descripcion Nueva descripción para la categoría.
	 * @param int|null    $prioridad   Nueva prioridad para la categoría (opcional).
	 * @param string|null $icono       Nuevo icono para la categoría (opcional).
	 * @param PDO         $conexion    Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $descripcion, ?int $prioridad, ?string $icono, PDO $conexion): bool
	{
		if (empty(trim($descripcion))) {
			throw new Exception("La descripción no puede estar vacía.");
		}
		
		try {
			// Consulta para actualizar la descripción, prioridad e icono
			$query = <<<SQL
            UPDATE servicios_categorias SET
                descripcion = :descripcion,
                prioridad = :prioridad,
                icono = :icono
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($descripcion), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $prioridad, PDO::PARAM_INT);
			$statement->bindValue(':icono', $icono, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada,
			// aunque si los datos son los mismos, rowCount será 0 pero la operación es "exitosa".
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al modificar categoría (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Desactiva una categoría estableciendo su estado a 0.
	 *
	 * @param int $id       ID de la categoría a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE servicios_categorias SET
            	estado = 0
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar categoría (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getIcono(): ?string
	{
		return $this->icono;
	}

	public function setIcono(?string $icono): self
	{
		$this->icono = $icono;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si la categoría está activa.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
	
	/**
	 * Obtiene los servicios asociados a esta categoría.
	 * @return array
	 */
	public function getServicios(): array
	{
		return $this->servicios;
	}
	
	/**
	 * Establece los servicios asociados a esta categoría.
	 * @param array $servicios Array de objetos Servicio
	 * @return self
	 */
	public function setServicios(array $servicios): self
	{
		$this->servicios = $servicios;
		return $this;
	}

}
