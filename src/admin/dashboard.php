<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\ConfigWeb;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en dashboard.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Initialize variables for form and feedback
$configData = null;
$success_text = '';
$success_display = 'hide';
$error_text = '';
$error_display = 'hide';

#region Handle Flash Messages
// Check for success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']); // Clear after preparing display
}

// Check for error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']); // Clear after preparing display
}
#endregion Handle Flash Messages

#region Load Existing Configuration Data
try {
	// Load existing configuration data to pre-populate the form
	$configData = ConfigWeb::getPrincipal($conexion);
} catch (Exception $e) {
	error_log("Error loading configuration data: " . $e->getMessage());
	// Continue without pre-populated data - form will be empty
}
#endregion Load Existing Configuration Data

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Reset feedback vars for this request attempt
	$success_text = '';
	$success_display = 'hide';
	$error_text = '';
	$error_display = 'hide';

	try {
		// 1. Get data from $_POST and clean it
		$telefono = !empty($_POST['telefono']) ? limpiar_datos($_POST['telefono']) : null;
		$correo = !empty($_POST['correo']) ? limpiar_datos($_POST['correo']) : null;
		$link_facebook = !empty($_POST['link_facebook']) ? limpiar_datos($_POST['link_facebook']) : null;
		$link_instagram = !empty($_POST['link_instagram']) ? limpiar_datos($_POST['link_instagram']) : null;
		$link_linkedin = !empty($_POST['link_linkedin']) ? limpiar_datos($_POST['link_linkedin']) : null;

		// 2. Validate email format if provided
		if (!empty($correo) && !filter_var($correo, FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo electrónico no es válido.");
		}

		// 3. Check if configuration exists and update or create accordingly
		$existingConfig = ConfigWeb::getPrincipal($conexion);

		if ($existingConfig) {
			// Update existing configuration
			$success = ConfigWeb::modificar(
				$existingConfig->getId(),
				$telefono,
				$correo,
				$link_facebook,
				$link_instagram,
				$link_linkedin,
				$conexion
			);

			if ($success) {
				$_SESSION['flash_message_success'] = "Configuración actualizada correctamente.";
				header('Location: dashboard');
				exit;
			} else {
				throw new Exception("No se pudo actualizar la configuración.");
			}
		} else {
			// Create new configuration
			$newConfig = new ConfigWeb();
			$newConfig->setTelefono($telefono)
					  ->setCorreo($correo)
					  ->setLinkFacebook($link_facebook)
					  ->setLinkInstagram($link_instagram)
					  ->setLinkLinkedin($link_linkedin);

			$newId = $newConfig->crear($conexion);

			if ($newId) {
				$_SESSION['flash_message_success'] = "Configuración creada correctamente.";
				header('Location: dashboard');
				exit;
			} else {
				throw new Exception("No se pudo crear la configuración.");
			}
		}

	} catch (PDOException $e) {
		// Handle database errors
		error_log("Database error in dashboard configuration: " . $e->getMessage());
		$error_text = 'Error de base de datos al guardar la configuración. Por favor, intente de nuevo más tarde.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle validation errors or other errors
		error_log("Error in dashboard configuration: " . $e->getMessage());
		$error_text = 'Error: ' . htmlspecialchars($e->getMessage());
		$error_display = 'show';
	}
}
#endregion POST Request Handling

require_once __ROOT__ . '/views/admin/dashboard.view.php';

?>