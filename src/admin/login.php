<?php
// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Usuario;

// Includes necesarios (asegúrate que las rutas y constantes sean correctas)
require_once dirname(__FILE__, 3) . '/config/config.php';  // Ajusta la ruta según tu estructura
require_once __ROOT__ . '/src/query/connection.php';       // Para obtener $conexion
require_once __ROOT__ . '/src/sessions/sessions.php';      // Contiene comprobar_sesion_login() y otras funciones de sesión
require_once __ROOT__ . '/src/general/general.php';        // Contiene recover_var() y otras funciones generales
require_once __ROOT__ . '/src/general/validaciones.php';   // Contiene limpiar_datos()

require_once __ROOT__ . '/vendor/autoload.php';

// Comprobar si el usuario ya está logueado y redirigir si es necesario
comprobar_sesion_login();                                  // Esta función debería redirigir si ya hay sesión activa

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en cerrar_caja.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Inicializar variables para el formulario y errores
$username_input = '';
$error_display  = null;
$error_text     = '';

#region Procesamiento del POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Guardar el username enviado para repoblar el campo en caso de error
	$username_input = limpiar_datos($_POST['usuario'] ?? '');
	
	try {
		// Limpiar datos de entrada
		$clave_input = limpiar_datos($_POST['clave'] ?? '');
		
		// Validación básica de campos no vacíos
		if (empty($username_input) || empty($clave_input)) {
			throw new Exception("Usuario y contraseña son requeridos.");
		}
		
		// Validar credenciales usando el método estático seguro de la clase Usuario
		$usuarioValidado = Usuario::validarCredenciales($username_input, $clave_input, $conexion);
		
		if ($usuarioValidado instanceof Usuario) {
			// ¡Éxito! Credenciales válidas
			session_regenerate_id(true); // Regenerar ID de sesión por seguridad
			
			// Guardar información del usuario en la sesión
			$_SESSION[USR_SESSION] = $usuarioValidado->getId();
			
			// Redirigir al dashboard principal para otros perfiles
			header('Location: dashboard');
			exit;
			
		} else {
			// Fallo de autenticación (usuario no encontrado, inactivo o contraseña incorrecta)
			$error_text    = "Usuario o contraseña incorrectos.";
			$error_display = 'show';
		}
		
	} catch (Exception $e) {
		// Capturar cualquier otra excepción (DB, validación inicial, etc.)
		error_log("Error en login: " . $e->getMessage()); // Registrar el error real para depuración
		// Mostrar un mensaje genérico al usuario
		$error_text    = ($e->getMessage() === "Usuario y contraseña son requeridos.")
			? $e->getMessage()
			: "Ocurrió un error durante el inicio de sesión. Por favor, inténtalo de nuevo.";
		$error_display = 'show';
	}
}
#endregion Procesamiento del POST

require_once __ROOT__ . '/views/admin/login.view.php';

?>