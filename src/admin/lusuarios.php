<?php

declare(strict_types=1);

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

// Initialize response array for AJAX requests
$response = ['success' => false, 'message' => ''];

/** @var PDO $conexion */
global $conexion;

use App\classes\Usuario;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lusuarios.php.");
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearUsuario($conexion, $response);
				break;
			
			case 'editar':
				handleEditarUsuario($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarUsuario($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerUsuario($conexion, $response);
				break;
			
			case 'cambiar_clave':
				handleCambiarClave($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lusuarios.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	// Return JSON response
	echo json_encode($response);
	exit;
}

// If not POST request, load the view
try {
	// Get list of active users for display
	$usuarios = Usuario::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar usuarios: " . $e->getMessage());
	$usuarios = [];
}

// Include the view
require_once __ROOT__ . '/views/admin/lusuarios.view.php';

/**
 * Handle creating a new user
 */
function handleCrearUsuario(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$username         = limpiar_datos($_POST['username'] ?? '');
		$password         = $_POST['password'] ?? '';
		$confirm_password = $_POST['confirm_password'] ?? '';
		$nombre           = limpiar_datos($_POST['nombre'] ?? '');
		
		// Server-side validation
		serverSideValidationCrear($username, $password, $confirm_password, $nombre);

		// Create new user object and populate it
		$usuario = new Usuario();
		$usuario->setUsername(trim($username))
			->setClave($password) // setClave() automatically hashes the password
			->setNombre(trim($nombre))
			->setEstado(1);

		// Use Usuario class method to create the user
		$newUserId = $usuario->crear($conexion);

		if ($newUserId) {
			$response['success'] = true;
			$response['message'] = 'Usuario creado exitosamente.';
		} else {
			throw new Exception('Error al crear el usuario.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle editing a user
 */
function handleEditarUsuario(PDO $conexion, array &$response): void
{
	try {
		$id     = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		$nombre = limpiar_datos($_POST['nombre'] ?? '');
		
		// Server-side validation
		if ($id <= 0) {
			throw new Exception('ID de usuario no válido.');
		}

		// Use Usuario::modificar() method
		if (Usuario::modificar($id, $nombre, $conexion)) {
			$response['success'] = true;
			$response['message'] = 'Usuario actualizado exitosamente.';
		} else {
			throw new Exception('Error al actualizar el usuario.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle deleting (deactivating) a user
 */
function handleEliminarUsuario(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de usuario no válido.');
		}
		
		// Use Usuario::desactivar() method
		if (Usuario::desactivar($id, $conexion)) {
			$response['success'] = true;
			$response['message'] = 'Usuario desactivado exitosamente.';
		} else {
			throw new Exception('Error al desactivar el usuario.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting a user for editing
 */
function handleObtenerUsuario(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de usuario no válido.');
		}
		
		// Use Usuario::get() method
		$usuario = Usuario::get($id, $conexion);
		
		if ($usuario && $usuario->getEstado() === 1) {
			$response['success'] = true;
			$response['data']    = [
				'id'       => $usuario->getId(),
				'username' => $usuario->getUsername(),
				'nombre'   => $usuario->getNombre()
			];
		} else {
			throw new Exception('Usuario no encontrado.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle changing user password
 */
function handleCambiarClave(PDO $conexion, array &$response): void
{
	try {
		$id               = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		$new_password     = $_POST['new_password'] ?? '';
		$confirm_password = $_POST['confirm_password'] ?? '';
		
		// Server-side validation
		if ($id <= 0) {
			throw new Exception('ID de usuario no válido.');
		}
		
		if (empty($new_password)) {
			throw new Exception('La nueva contraseña es requerida.');
		}
		
		if (strlen($new_password) < 5) {
			throw new Exception('La contraseña debe tener al menos 5 caracteres.');
		}
		
		if ($new_password !== $confirm_password) {
			throw new Exception('Las contraseñas no coinciden.');
		}
		
		// Use Usuario::actualizarClave() method
		if (Usuario::actualizarClave($id, $new_password, $conexion)) {
			$response['success'] = true;
			$response['message'] = 'Contraseña actualizada exitosamente.';
		} else {
			throw new Exception('Error al actualizar la contraseña.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Server-side validation for creating users
 * @throws Exception
 */
function serverSideValidationCrear(string $username, string $password, string $confirm_password, string $nombre): void
{
	if (empty(trim($username))) {
		throw new Exception('El username es requerido.');
	}
	
	if (strlen(trim($username)) < 3) {
		throw new Exception('El username debe tener al menos 3 caracteres.');
	}
	
	if (empty($password)) {
		throw new Exception('La contraseña es requerida.');
	}
	
	if (strlen($password) < 5) {
		throw new Exception('La contraseña debe tener al menos 5 caracteres.');
	}
	
	if ($password !== $confirm_password) {
		throw new Exception('Las contraseñas no coinciden.');
	}
	
	if (empty(trim($nombre))) {
		throw new Exception('El nombre es requerido.');
	}
	
	if (strlen(trim($nombre)) < 2) {
		throw new Exception('El nombre debe tener al menos 2 caracteres.');
	}
}
