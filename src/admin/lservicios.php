<?php

declare(strict_types=1);

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

// Initialize response array for AJAX requests
$response = ['success' => false, 'message' => ''];

/** @var PDO $conexion */
global $conexion;

use App\classes\Servicio;
use App\classes\ServicioCategoria;
use App\classes\Usuario;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lservicios.php.");
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearServicio($conexion, $response);
				break;
			
			case 'editar':
				handleEditarServicio($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarServicio($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerServicio($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lservicios.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	echo json_encode($response);
	exit;
}

// Handle GET requests - Load services and categories list
try {
	$servicios = Servicio::get_list($conexion);
	$categorias = ServicioCategoria::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar servicios: " . $e->getMessage());
	$servicios = [];
	$categorias = [];
}

// Include the view
require_once __ROOT__ . '/views/admin/lservicios.view.php';

/**
 * Handle creating a new service
 */
function handleCrearServicio(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$id_categoria = isset($_POST['id_servicio_categoria']) ? (int)$_POST['id_servicio_categoria'] : null;
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		// Server-side validation
		serverSideValidation($descripcion, $id_categoria, $prioridad);
		
		// Create new service object
		$servicio = new Servicio();
		$servicio->setDescripcion(trim($descripcion))
			->setId_servicio_categoria($id_categoria)
			->setPrioridad($prioridad)
			->setEstado(1);
		
		// Save to database
		$newId = $servicio->crear($conexion);
		
		if ($newId) {
			$response['success'] = true;
			$response['message'] = 'Servicio creado exitosamente.';
			$response['id'] = $newId;
		} else {
			throw new Exception('No se pudo crear el servicio.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Server-side validation for service data
 * @throws Exception
 */
function serverSideValidation($descripcion, ?int $id_categoria, ?int $prioridad): void
{
	if (empty(trim($descripcion))) {
		throw new Exception('La descripción es requerida.');
	}
	
	if (strlen(trim($descripcion)) < 3) {
		throw new Exception('La descripción debe tener al menos 3 caracteres.');
	}
	
	if (strlen(trim($descripcion)) > 255) {
		throw new Exception('La descripción no puede exceder 255 caracteres.');
	}
	
	if ($id_categoria === null || $id_categoria <= 0) {
		throw new Exception('Debe seleccionar una categoría de servicio.');
	}
	
	if ($prioridad === null || $prioridad <= 0) {
		throw new Exception('La prioridad debe ser un número positivo.');
	}
}

/**
 * Handle editing an existing service
 */
function handleEditarServicio(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$id_categoria = isset($_POST['id_servicio_categoria']) ? (int)$_POST['id_servicio_categoria'] : null;
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		if ($id <= 0) {
			throw new Exception('ID de servicio no válido.');
		}
		
		// Server-side validation (same as create)
		serverSideValidation($descripcion, $id_categoria, $prioridad);
		
		// Update service
		$success = Servicio::modificar($id, trim($descripcion), $id_categoria, $prioridad, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Servicio actualizado exitosamente.';
		} else {
			throw new Exception('No se pudo actualizar el servicio.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle soft deleting a service
 */
function handleEliminarServicio(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de servicio no válido.');
		}
		
		// Soft delete (deactivate)
		$success = Servicio::desactivar($id, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Servicio eliminado exitosamente.';
		} else {
			throw new Exception('No se pudo eliminar el servicio.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting service data for editing
 */
function handleObtenerServicio(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de servicio no válido.');
		}
		
		$servicio = Servicio::get($id, $conexion);
		
		if ($servicio) {
			$response['success'] = true;
			$response['data'] = [
				'id' => $servicio->getId(),
				'descripcion' => $servicio->getDescripcion(),
				'id_servicio_categoria' => $servicio->getId_servicio_categoria(),
				'prioridad' => $servicio->getPrioridad()
			];
		} else {
			throw new Exception('Servicio no encontrado.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

?>
