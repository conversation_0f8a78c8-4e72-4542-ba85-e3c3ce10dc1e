<?php

declare(strict_types=1);

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\ServicioCategoria;
use App\classes\Usuario;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lservicios_categorias.php.");
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Initialize response array for AJAX requests
$response = ['success' => false, 'message' => ''];

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearCategoria($conexion, $response);
				break;
			
			case 'editar':
				handleEditarCategoria($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarCategoria($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerCategoria($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lservicios_categorias.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	echo json_encode($response);
	exit;
}

// Handle GET requests - Load categories list
try {
	$categorias = ServicioCategoria::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar categorías: " . $e->getMessage());
	$categorias = [];
}

// Include the view
require_once __ROOT__ . '/views/admin/lservicios_categorias.view.php';

/**
 * Handle creating a new category
 */
function handleCrearCategoria(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$icono       = limpiar_datos($_POST['icono'] ?? '');
		$prioridad   = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		// Server-side validation
		serverSideValidation($descripcion, $icono, $prioridad);
		
		// Create new category object
		$categoria = new ServicioCategoria();
		$categoria->setDescripcion(trim($descripcion))
			->setIcono(trim($icono))
			->setPrioridad($prioridad)
			->setEstado(1);
		
		// Save to database
		$newId = $categoria->crear($conexion);
		
		if ($newId) {
			$response['success'] = true;
			$response['message'] = 'Categoría creada exitosamente.';
			$response['id']      = $newId;
		} else {
			throw new Exception('No se pudo crear la categoría.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * @param          $descripcion
 * @param          $icono
 * @param int|null $prioridad
 *
 * @return void
 * @throws Exception
 */
function serverSideValidation($descripcion, $icono, ?int $prioridad): void
{
	if (empty(trim($descripcion))) {
		throw new Exception('La descripción es requerida.');
	}
	
	if (strlen(trim($descripcion)) < 3) {
		throw new Exception('La descripción debe tener al menos 3 caracteres.');
	}
	
	if (strlen(trim($descripcion)) > 100) {
		throw new Exception('La descripción no puede exceder 100 caracteres.');
	}
	
	if (empty(trim($icono))) {
		throw new Exception('El icono es requerido.');
	}
	
	if ($prioridad === null || $prioridad <= 0) {
		throw new Exception('La prioridad debe ser un número positivo.');
	}
}

/**
 * Handle editing an existing category
 */
function handleEditarCategoria(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$id          = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$icono       = limpiar_datos($_POST['icono'] ?? '');
		$prioridad   = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		if ($id <= 0) {
			throw new Exception('ID de categoría no válido.');
		}
		
		// Server-side validation (same as create)
		serverSideValidation($descripcion, $icono, $prioridad);
		
		// Update category
		$success = ServicioCategoria::modificar($id, trim($descripcion), $prioridad, trim($icono), $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Categoría actualizada exitosamente.';
		} else {
			throw new Exception('No se pudo actualizar la categoría.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle soft deleting a category
 */
function handleEliminarCategoria(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de categoría no válido.');
		}
		
		// Soft delete (deactivate)
		$success = ServicioCategoria::desactivar($id, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Categoría eliminada exitosamente.';
		} else {
			throw new Exception('No se pudo eliminar la categoría.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting category data for editing
 */
function handleObtenerCategoria(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de categoría no válido.');
		}
		
		$categoria = ServicioCategoria::get($id, $conexion);
		
		if ($categoria) {
			$response['success'] = true;
			$response['data']    = [
				'id'          => $categoria->getId(),
				'descripcion' => $categoria->getDescripcion(),
				'icono'       => $categoria->getIcono(),
				'prioridad'   => $categoria->getPrioridad()
			];
		} else {
			throw new Exception('Categoría no encontrada.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

?>
