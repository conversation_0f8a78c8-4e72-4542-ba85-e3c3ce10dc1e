<?php

use App\classes\Usuario;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available via preparar.php

// --- Include necessary files ---
require_once dirname(__DIR__, 2) . '/config/config.php';       // Defines __ROOT__, constants etc.
require_once __ROOT__ . '/src/sessions/sessions.php';           // Needed for session functions maybe, and USR_SESSION constant
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';        // Included as requested
require_once __ROOT__ . '/src/general/preparar.php';            // Included as requested (handles login check, feedback var init)
// --- End Include files ---

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en cerrar_caja.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region Handle Flash Messages (from previous actions, e.g., successful save)
// Check for a success flash message from session (set by this page on redirect)
// Note: preparar.php initializes the variables, but we still need to check the session
// *after* a redirect to display the message set before the redirect.
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']); // Clear after preparing display
}
// Check for an error flash message from session (less common here)
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']); // Clear after preparing display
}
#endregion Handle Flash Messages

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Reset feedback vars for this request attempt
	$success_text    = '';
	$success_display = 'hide';
	$error_text      = '';
	$error_display   = 'hide';
	
	try {
		// Ensure user ID is available (should be guaranteed by preparar.php)
		if (empty($_SESSION[USR_SESSION])) {
			// This should ideally not happen if preparar.php works correctly, but as a safeguard:
			throw new Exception("Sesión de usuario no encontrada. Por favor, inicie sesión de nuevo.");
		}
		$loggedInUserId = (int)$_SESSION[USR_SESSION]; // Get the logged-in user's ID using the constant
		
		// 1. Get data from $_POST
		$nueva_clave   = $_POST['nueva_clave'] ?? ''; // Don't trim passwords
		$confirm_clave = $_POST['confirm_clave'] ?? '';
		
		// 2. Validate data
		if (empty($nueva_clave)) {
			throw new Exception("La nueva contraseña no puede estar vacía.");
		}
		if ($nueva_clave !== $confirm_clave) {
			throw new Exception("Las contraseñas no coinciden.");
		}
		// Optional: Add password strength validation here if desired
		
		// 3. Call the static method to update the password
		$success = Usuario::actualizarClave($loggedInUserId, $nueva_clave, $conexion);
		
		if ($success) {
			// Set success message in session FOR THE NEXT PAGE LOAD (after redirect)
			$_SESSION['flash_message_success'] = "Contraseña actualizada correctamente.";
			header('Location: cambiar_clave'); // Redirect back to the same page
			exit;
		} else {
			// This might happen if the user ID somehow became invalid
			throw new Exception("No se pudo actualizar la contraseña. Es posible que el usuario ya no exista.");
		}
		
	} catch (PDOException $e) {
		// Handle potential database errors during update
		// Log the detailed error: error_log("Database error changing password for user $loggedInUserId: " . $e->getMessage());
		$error_text    = 'Error de base de datos al cambiar la contraseña. Por favor, intente de nuevo más tarde.';
		$error_display = 'show'; // Display error on the current page load
	} catch (Exception $e) {
		// Handle validation errors or other errors from actualizarClave
		// Log the detailed error: error_log("Error changing password for user $loggedInUserId: " . $e->getMessage());
		$error_text    = 'Error: ' . htmlspecialchars($e->getMessage()); // Escape output
		$error_display = 'show'; // Display error on the current page load
	}
}
#endregion POST Request Handling

// Load the view file.
// Variables $success_text, $success_display, $error_text, $error_display
// will be available, either from flash messages or from the POST handling logic above.
require_once __ROOT__ . '/views/admin/cambiar_clave.view.php';
?>