<?php
global $conexion, $lastmodule;

use App\classes\Usuario;

// BEGIN redirect SSL
// funcion para obligar todo el trafico de la pagina a traves del canal SSL.

if ((empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off") && ($_SERVER['HTTP_HOST'] !== "localhost" && $_SERVER['HTTP_HOST'] !== "127.0.0.1")) {
	$location = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
	header('HTTP/1.1 301 Moved Permanently');
	header('Location: ' . $location);
	exit();
}
// END redirect SSL

require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/vendor/autoload.php';

#region try
try {
	//comprar que el usuario este logueado
	comprobar_sesion();
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

$dashboard_usuario_info = new Usuario;

try {
	$logged_usuario_info = Usuario::get($_SESSION[USR_SESSION], $conexion);
	
	// verificar si el usuario esta activo
	if ($dashboard_usuario_info->getEstado() == 0) {
		// si no esta activo, cerrar sesion
		header('Location: cerrar');
		exit();
	}
	
	$error_text      = '';
	$error_display   = 'hide';
	$success_text    = '';
	$success_display = 'hide';
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}


?>