<?php
// -general
function desordena($data): string
{
    $key = PASS;
    $encryption_key = base64_decode($key);
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));
    $encrypted = openssl_encrypt($data, 'aes-256-cbc', $encryption_key, 0, $iv);

    return base64_encode($encrypted . '::' . $iv);
}

function ordena($data)
{
    $key = PASS;
    $encryption_key = base64_decode($key);
    list($encrypted_data, $iv) = array_pad(explode('::', base64_decode($data), 2), 2, null);

    return openssl_decrypt($encrypted_data, 'aes-256-cbc', $encryption_key, 0, $iv);
}

function limpiar_datos($datos)
{
    $datos = trim($datos);
    $datos = stripslashes($datos);
    $datos = htmlspecialchars($datos);
    $datos = preg_replace('/\x00|<[^>]*>?/', '', $datos);
    $datos = str_replace(["'", '"'], ['&#39;', '&#34;'], $datos);

    return $datos;
}

function recover_var($dato)
{
    if (isset($dato)) return limpiar_datos($dato);
}

function recover_var_list($dato, $rowid)
{
    if ($dato == $rowid) echo 'selected="selected"';
}

function recuperar_estado_checked($dato): string
{
    if ($dato == 1) {
        return 'checked="checked"';
    } else{
        return '';
    }
}

function recoverradio($valuecontrol, $valueform)
{
    if ($valuecontrol == $valueform) echo 'checked="checked"';
}

function getvalue_checkbox($postvalue): int
{
    if (isset($postvalue)) {
        return 1;
    } else {
        return 0;
    }
}

function formatnumpositive($number)
{
    if ($number >= 0) {
        return "+" . $number;
    } else {
        return $number;
    }
}

function format_currency($numero)
{
    if (is_numeric($numero)) {
        $numero = round($numero, 0);
        $pos = strpos($numero, '.');

        if ($pos === false) {
            return number_format($numero, 0, ',', '.');

        } else {
            return $numero;
        }
    } else {
        return $numero;
    }
}

function format_currency_usd($numero)
{
    return number_format($numero, 2, '.', ',');
}

function format_currency_consigno($numero): float|string
{
    $numero = round($numero, 0);
    $pos = strpos($numero, '.');

    if ($pos === false) {
        $numeroformateado = number_format(abs($numero), 0, ',', '.');

        if (intval($numero) < 0) {
            $numeroformateado = "-$" . $numeroformateado;

        } else {
            $numeroformateado = "$" . $numeroformateado;
        }

        return $numeroformateado;
    } else {
        return $numero;
    }
}

function format_numberclean($number)
{
    $number = str_replace("$", "", $number);
    $number = str_replace(" ", "", $number);
    $number = str_replace(".", "", $number);
    $number = str_replace(",", "", $number);

    return $number;
}

function format_date($fecha)
{
    if (!empty($fecha)) {
        $timestamp = strtotime($fecha);
        $meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];

        $diasem = date('D', $timestamp);
        $dia = date('d', $timestamp);
        $mes = date('m', $timestamp) - 1;
        $year = date('y', $timestamp);

        $fecha = "$diasem, $meses[$mes] $dia, $year";

        return $fecha;
    } else {
        return '';
    }
}

function format_date2($fecha)
{
    if (!empty($fecha)) {
        $timestamp = strtotime($fecha);
        $meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];

        $dia = date('d', $timestamp);
        $mes = date('m', $timestamp) - 1;
        $year = date('y', $timestamp);

        $fecha = "$meses[$mes] $dia, $year";

        return $fecha;
    } else {
        return '';
    }
}

function format_dateyyymmdd($fecha)
{
    if (!empty($fecha)) {
        $timestamp = strtotime($fecha);

        $dia = date('d', $timestamp);
        $mes = date('m', $timestamp);
        $year = date('Y', $timestamp);

        $fecha = "$year-$mes-$dia";

        return $fecha;
    } else {
        return '';
    }
}

function create_date()
{
    date_default_timezone_set("America/Bogota");

    $fecha = date("Y-m-d");

    return $fecha;
}

function create_date_thismonth()
{
    date_default_timezone_set("America/Bogota");

    $fecha = date("Y-m-d");
    $timestamp = strtotime($fecha);

    $year = date('Y', $timestamp);
    $mes = date('m', $timestamp);

    return $year . "-" . $mes . "-01";
}

function create_date_thisyear()
{
    date_default_timezone_set("America/Bogota");

    $fecha = date("Y-m-d");
    $timestamp = strtotime($fecha);

    $year = date('Y', $timestamp);

    return $year;
}

function create_datetime()
{
    date_default_timezone_set("America/Bogota");

    $fecha = date("Y-m-d H:i:s");

    return $fecha;
}

function create_datetimepegado()
{
    date_default_timezone_set("America/Bogota");

    $fecha = date("YmdHis");

    return $fecha;
}

function get_month()
{
    $fecha = create_date();
    $timestamp = strtotime($fecha);
    $mes = date('m', $timestamp);

    return $mes;
}

function get_year()
{
    $fecha = create_date();
    $timestamp = strtotime($fecha);
    $year = date('Y', $timestamp);

    return $year;
}

function get_pagename()
{
    return str_replace(".php", "", basename($_SERVER['PHP_SELF']));
}

/**
 * @throws Exception
 */
function splitDaterange($fchrange): array
{
    try {
        $fch = array();

        if (!empty($fchrange)) {
            $fch = explode(" - ", $fchrange);
        }

        return $fch;

    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

function getDateDiff($completedate1, $completedate2)
{
    $datetime1 = new DateTime($completedate1);
    $datetime2 = new DateTime($completedate2);

    return date_diff($datetime1, $datetime2);
}

function getDateDiffDays($completedate1, $completedate2)
{
    $interval = getDateDiff($completedate1, $completedate2);

    //return number of days. %a
    return $interval->format('%a');
}

function sanitize_phone_number($input): array|string
{
	// Remove spaces, parentheses, and any other unwanted characters
	return str_replace([' ', '(', ')'], '', $input);
}

?>