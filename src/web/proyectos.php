<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

/**
 * Determines the media type based on file extension
 *
 * @param string $filename The filename to analyze
 *
 * @return string 'image' or 'video'
 */
function getMediaType(string $filename): string
{
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    $videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

    if (in_array($extension, $videoExtensions)) {
        return 'video';
    } elseif (in_array($extension, $imageExtensions)) {
        return 'image';
    }

    // Default to image for unknown extensions
    return 'image';
}

/**
 * Processes media files array to include type information
 * @param array $files Array of filenames
 * @return array Array of media objects with file and type properties
 */
function processMediaFiles($files): array
{
    $processedFiles = [];
    foreach ($files as $file) {
        $processedFiles[] = [
            'file' => $file,
            'type' => getMediaType($file)
        ];
    }
    return $processedFiles;
}

#region region init variables
$proyectos = [
    [
        'id' => 1,
        'client_name' => 'CONSTRUCTORA BOLIVAR',
        'project_name' => 'PARQUES DE BOLIVAR SOLEDAD',
        'location' => 'Soledad, Atlántico',
        'description' => 'Construcción de red eléctrica de media y baja tensión de distribución y alumbrado público.',
        'client_logo' => 'seguros-bolivar.png',
        'featured_image' => '1_1.jpg',
        'images' => [
            '1_1.jpg',
            '1_2.jpg'
        ]
    ],
    [
        'id' => 2,
        'client_name' => 'CONSTRUCTORA BOLIVAR ',
        'project_name' => 'PROYECTO TORCAZA - ALAMEDA DEL RIO',
        'location' => 'Barranquilla, Atlántico',
        'description' => 'Montaje y energización de subestaciones eléctricas para provisional de obra.',
        'client_logo' => 'seguros-bolivar.png',
        'featured_image' => '2_1.jpg',
        'images' => [
	        '2_1.jpg'
        ]
    ],
    [
        'id' => 3,
        'client_name' => 'PARQUES SOLARES',
        'project_name' => 'DESBROCE DE VEGETACIÓN Y PODA DE ÁRBOLES EN PLANTAS SOLARES',
        'location' => 'Colombia',
        'description' => 'Servicio de desbroce de vegetación y poda técnica de árboles en plantas de generación solar y áreas generales.',
        'client_logo' => 'desbroce.png',
        'featured_image' => '3_1.jpeg',
        'images' => [
            '3_1.jpeg',
            '3_2.jpeg',
            '3_3.jpeg',
            '3_4.jpeg',
            '3_5.jpeg',
            '3_6.jpeg',
            '3_7.jpeg',
            '3_8.jpeg',
            '3_9.jpeg',
            '3_10.mp4',
            '3_11.mp4',
            '3_12.mp4',
            '3_13.mp4',
            '3_14.mp4',
            '3_15.mp4',
            '3_16.mp4',
            '3_17.mp4'
        ]
    ],
    [
        'id' => 4,
        'client_name' => 'E.S.E. HOSPITAL SANTA MARIA MAGDALENA DE MALAMBO',
        'project_name' => 'REPOTENCIACIÓN DE SUBESTACIÓN ELÉCTRICA',
        'location' => 'Malambo, Atlántico',
        'description' => 'Suministro, montaje y puesta en operación de grupo electrógeno de 200 KVA para soporte de red eléctrica de emergencia.',
        'client_logo' => 'hospital-malambo.png',
        'featured_image' => '4_1.jpg',
        'images' => [
            '4_1.jpg',
            '4_2.jpg',
            '4_3.jpg'
        ]
    ],
    [
        'id' => 5,
        'client_name' => 'EDS SANTA MARÍA DEL MAR',
        'project_name' => 'RETRANQUEO RED AÉREA DE MEDIA TENSIÓN 34,5 kV',
        'location' => 'Barranquilla, Atlántico',
        'description' => 'Retranqueo de redes aéreas de media tensión de 34,5 kV, Línea 574 que interconecta la Subestación Las Flores y Puerta de Oro, de propiedad de Air-e, y Línea 508 de Monómeros a 34,5 kV.',
        'client_logo' => 'terpel.png',
        'featured_image' => '5_1.jpg',
        'images' => [
            '5_1.jpg',
            '5_2.jpg',
            '5_3.jpg',
            '5_4.jpg',
            '5_5.jpg',
            '5_6.jpg',
            '5_7.jpg',
            '5_8.jpg',
            '5_9.mp4'
        ]
    ],
    [
        'id' => 6,
        'client_name' => 'SOCIEDAD PORTUARIA RIO CORDOBA',
        'project_name' => 'MANTENIMIENTO RED AÉREA DE MEDIA TENSIÓN 34,5 kV Y RED DE ALUMBRADO PÚBLICO',
        'location' => 'Río Córdoba, Magdalena',
        'description' => 'Mantenimiento preventivo y correctivo de red de media tensión y alumbrado público.',
        'client_logo' => 'sociedad-portuaria.png',
        'featured_image' => '6_1.jpg',
        'images' => [
            '6_1.jpg',
            '6_2.jpg'
        ]
    ],
    [
        'id' => 7,
        'client_name' => 'TECNOGLASS',
        'project_name' => 'LIMPIEZA Y LAVADO DE DUCTOS DE MEDIA TENSIÓN',
        'location' => 'Barranquilla, Atlántico',
        'description' => 'Mantenimiento y limpieza de ductos de media tensión con equipo especializado Vactor.',
        'client_logo' => 'tecnoglass.jpg',
        'featured_image' => '7_1.jpg',
        'images' => [
            '7_1.jpg',
            '7_2.jpg',
            '7_3.jpg',
            '7_4.jpg'
        ]
    ],
    [
        'id' => 8,
        'client_name' => 'GRANJA SOLAR',
        'project_name' => 'PANELES SOLARES',
        'location' => 'Colombia',
        'description' => 'Diseño, construcción, puesta en servicio, operación y mantenimiento de sistema solar fotovoltaico, en sector residencial e industrial.',
        'client_logo' => 'granja-solar.png',
        'featured_image' => '8_1.jpg',
        'images' => [
            '8_1.jpg',
            '8_2.jpg',
            '8_3.jpg',
            '8_4.mp4',
            '8_5.mp4'
        ]
    ],
    [
        'id' => 9,
        'client_name' => '',
        'project_name' => 'ATENCIÓN DE EMERGENCIA 24/7',
        'location' => 'Colombia',
        'description' => 'Atención de emergencia 24/7 en subestaciones y redes de media y baja tensión (en tensión y desenergizado).',
        'client_logo' => 'cliente-poster.png',
        'featured_image' => '9_1.jpg',
        'images' => [
            '9_1.jpg',
            '9_2.jpg',
            '9_3.jpg',
            '9_4.jpg',
            '9_5.jpg',
            '9_6.jpg',
            '9_7.jpg',
            '9_8.jpg',
            '9_9.jpg'
        ]
    ]
];

// Process media files for each project to include type information
foreach ($proyectos as &$proyecto) {
    $proyecto['media'] = processMediaFiles($proyecto['images']);
}
unset($proyecto); // Important: unset the reference to avoid issues
#endregion init variables

require_once __ROOT__ . '/views/web/proyectos.view.php';

?>
