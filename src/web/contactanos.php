<?php

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

#region region init variables
$nombre   = '';
$correo   = '';
$telefono = '';
$asunto   = '';
$mensaje  = '';
#endregion init variables
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
	
	
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$nombre   = limpiar_datos($_POST['nombre']);
		$correo   = limpiar_datos($_POST['correo']);
		$telefono = limpiar_datos($_POST['telefono']);
		$asunto   = limpiar_datos($_POST['asunto']);
		$mensaje  = limpiar_datos($_POST['mensaje']);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo
#region sub_enviar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_enviado'])) {
	try {
		$param             = array();
		$param['nombre']   = $nombre;
		$param['correo']   = $correo;
		$param['telefono'] = $telefono;
		$param['asunto']   = $asunto;
		$param['mensaje']  = $mensaje;
		PHPMailerCorreo::enviar_correo_contactanos($param);
		
		$success_display = 'show';
		$success_text    = 'El mensaje ha sido enviado.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_enviar

require_once __ROOT__ . '/views/web/contactanos.view.php';

?>